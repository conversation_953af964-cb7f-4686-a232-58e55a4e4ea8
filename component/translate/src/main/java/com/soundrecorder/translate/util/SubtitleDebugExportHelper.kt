/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - SubtitleDebugExportHelper.kt
 * Description:
 *     Export debug dump for ASR if enabled.
 *
 * Version: 1.0
 * Date: 2025-07-05
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2025-07-05   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.translate.util

import android.os.Environment
import androidx.annotation.WorkerThread
import com.google.gson.Gson
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.OSDKCompatUtils
import com.soundrecorder.common.constant.RecorderConstant
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.utils.SingleThread
import com.soundrecorder.translate.asr.bean.AsrResult
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.OutputStream
import java.io.PrintWriter
import java.nio.file.Files
import java.util.UUID
import java.util.concurrent.Executors
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.TimeUnit

/**
 * 实时字幕功能的调试Dump，默认不启用。
 * 启用方法(整机root版本，非root版本无法启用)：
 *   adb shell setprop debug.oplus.soundrecorder.subtitle.dump 1
 * 启用后，将导出调试内容至以下路径(timestamp为dump init时获取时间戳，uuid为随机生成)：
 *   /sdcard/Music/debug_recorder_subtitle/${timestamp}_${uuid}
 * 该路径下的导出内容如下：
 *  1.用于ASR的原始PCM音频：audio_${timestamp}_${uuid}.pcm
 *  2.原始ASR结果(含channelId等)：origin_asr_${timestamp}_${uuid}.txt
 *  3.过滤后的原始ASR结果：filter_asr_${timestamp}_${uuid}.txt
 *  4.合并拆分后的ASR结果: merged_asr_${timestamp}_${uuid}.txt
 *  5.大模型处理ASR的记录：llm_asr_${timestamp}_${uuid}.txt
 */
object SubtitleDebugExportHelper {

    private const val TAG = "SubtitleDebugExportHelper"
    private const val DIR_DEBUG_ROOT = "debug_recorder_subtitle"
    private const val FILE_AUDIO_PCM_PREFIX = "audio"
    private const val FILE_AUDIO_PCM_SUFFIX = ".pcm"
    private const val FILE_TXT_ORIGIN_ASR_PREFIX = "origin_asr"
    private const val FILE_TXT_FILTER_ASR_PREFIX = "filter_asr"
    private const val FILE_TXT_MERGED_ASR_PREFIX = "merged_asr"
    private const val FILE_TXT_LLM_ASR_PREFIX = "llm_asr"
    private const val FILE_TXT_SUFFIX = ".txt"
    private const val FILE_NAME_SPLIT_CHAR = "_"
    private const val PROP_DEBUG_DUMP = "debug.oplus.soundrecorder.subtitle.dump"
    private const val DEBUG_DUMP_ENABLE = "1"
    private const val DEBUG_DUMP_DISABLE = "0"
    private const val PCM_QUEUE_CAPACITY = RecorderConstant.REALTIME_PCM_QUEUE_CAPACITY
    private const val PCM_QUEUE_TIMEOUT = RecorderConstant.REALTIME_PCM_QUEUE_TIMEOUT

    private val gson by lazy { Gson() }
    private val enableDump by lazy {
        OSDKCompatUtils.getFeatureProperty(PROP_DEBUG_DUMP, DEBUG_DUMP_DISABLE) == DEBUG_DUMP_ENABLE
    }

    @Volatile
    private var exportExecutor: ExportExecutor? = null

    @JvmStatic
    private fun buildFileName(
        exportDirName: String,
        prefix: String,
        suffix: String = FILE_TXT_SUFFIX
    ): String = StringBuilder(prefix)
        .append(FILE_NAME_SPLIT_CHAR)
        .append(exportDirName)
        .append(suffix)
        .toString()

    @JvmStatic
    @Synchronized
    fun init() {
        if (!enableDump) {
            return
        }
        runCatching {
            exportExecutor = ExportExecutor().also {
                DebugUtil.d(TAG, "init: ${it.exportDirName}")
            }
        }.onFailure {
            DebugUtil.e(TAG, "init: error: $it")
        }
    }

    @JvmStatic
    @Synchronized
    fun export(): ExportExecutor? {
        if (!enableDump) {
            return null
        }
        return exportExecutor
    }

    @JvmStatic
    @Synchronized
    fun release() {
        if (!enableDump) {
            return
        }
        exportExecutor?.release()
        exportExecutor = null
    }

    class ExportExecutor internal constructor(
        internal val exportDirName: String = "${System.currentTimeMillis()}_${UUID.randomUUID()}"
    ) {
        private val contentExportThread = Executors.newSingleThreadExecutor()
        private val pcmExportThread = SingleThread("pcmExportThread")
        private val pcmBlockingQueue = LinkedBlockingQueue<ByteArray>(PCM_QUEUE_CAPACITY)
        private val exportRoot: File
        private val audioPcm: File
        private val originAsr: File
        private val filterAsr: File
        private val mergedAsr: File
        private val llmAsr: File

        @Volatile
        private var isReleased = false

        @Volatile
        private var audioPcmOutput: FileOutputStream? = null

        @Volatile
        private var originAsrOutput: PrintWriter? = null

        @Volatile
        private var filterAsrOutput: PrintWriter? = null

        @Volatile
        private var mergedAsrOutput: PrintWriter? = null

        @Volatile
        private var llmAsrOutput: PrintWriter? = null

        init {
            val musicDir =
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MUSIC)
            val debugDir = File(musicDir, DIR_DEBUG_ROOT)
            exportRoot = File(debugDir, exportDirName)
            exportRoot.mkdirs()
            val pcmFileName = buildFileName(exportDirName, FILE_AUDIO_PCM_PREFIX, FILE_AUDIO_PCM_SUFFIX)
            audioPcm = File(exportRoot, pcmFileName)
            Files.deleteIfExists(audioPcm.toPath())
            val originAsrName = buildFileName(exportDirName, FILE_TXT_ORIGIN_ASR_PREFIX)
            originAsr = File(exportRoot, originAsrName)
            Files.deleteIfExists(originAsr.toPath())
            val filterAsrName = buildFileName(exportDirName, FILE_TXT_FILTER_ASR_PREFIX)
            filterAsr = File(exportRoot, filterAsrName)
            Files.deleteIfExists(filterAsr.toPath())
            val mergedAsrName = buildFileName(exportDirName, FILE_TXT_MERGED_ASR_PREFIX)
            mergedAsr = File(exportRoot, mergedAsrName)
            Files.deleteIfExists(mergedAsr.toPath())
            val llmAsrName = buildFileName(exportDirName, FILE_TXT_LLM_ASR_PREFIX)
            llmAsr = File(exportRoot, llmAsrName)
            Files.deleteIfExists(llmAsr.toPath())
        }

        @Throws(IOException::class)
        private fun audioPcmOutput(action: OutputStream.() -> Unit) = synchronized(audioPcm) {
            audioPcmOutput?.let {
                it.action()
                return@synchronized
            }
            FileOutputStream(audioPcm).also {
                DebugUtil.d(TAG, "audioPcmOutput: open ${audioPcm.absolutePath}")
                audioPcmOutput = it
                it.action()
            }
        }

        @Throws(IOException::class)
        private fun originAsrOutput(action: PrintWriter.() -> Unit) = synchronized(originAsr) {
            originAsrOutput?.let {
                it.action()
                return@synchronized
            }
            PrintWriter(originAsr).also {
                DebugUtil.d(TAG, "originAsrOutput: open ${originAsr.absolutePath}")
                originAsrOutput = it
                it.action()
            }
        }

        @Throws(IOException::class)
        private fun filterAsrOutput(action: PrintWriter.() -> Unit) = synchronized(filterAsr) {
            filterAsrOutput?.let {
                it.action()
                return@synchronized
            }
            PrintWriter(filterAsr).also {
                DebugUtil.d(TAG, "filterAsrOutput: open ${filterAsr.absolutePath}")
                filterAsrOutput = it
                it.action()
            }
        }

        @Throws(IOException::class)
        private fun mergedAsrOutput(action: PrintWriter.() -> Unit) = synchronized(mergedAsr) {
            mergedAsrOutput?.let {
                it.action()
                return@synchronized
            }
            PrintWriter(mergedAsr).also {
                DebugUtil.d(TAG, "mergedAsrOutput: open ${mergedAsr.absolutePath}")
                mergedAsrOutput = it
                it.action()
            }
        }

        @Throws(IOException::class)
        private fun llmAsrOutput(action: PrintWriter.() -> Unit) = synchronized(llmAsr) {
            llmAsrOutput?.let {
                it.action()
                return@synchronized
            }
            PrintWriter(llmAsr).also {
                DebugUtil.d(TAG, "llmAsrOutput: open ${llmAsr.absolutePath}")
                llmAsrOutput = it
                it.action()
            }
        }

        fun writeAudioPcm(pcmBytes: ByteArray) {
            synchronized(this) { if (isReleased) return }
            pcmBlockingQueue.offer(pcmBytes)
            pcmExportThread.post {
                synchronized(this) { if (isReleased) return@post }
                runCatching {
                    var writePcm: ByteArray? = null
                    do {
                        writePcm = pcmBlockingQueue.poll(PCM_QUEUE_TIMEOUT, TimeUnit.MILLISECONDS)
                        audioPcmOutput {
                            write(writePcm)
                            flush()
                        }
                        synchronized(this) { if (isReleased) return@post }
                    } while (writePcm != null)
                }.onFailure {
                    DebugUtil.e(TAG, "writeAudioPcm: error: $it")
                }
            }
        }

        fun writeOriginAsr(channelId: String?, asrResult: AsrResult) {
            synchronized(this) { if (isReleased) return }
            contentExportThread.execute {
                synchronized(this) { if (isReleased) return@execute }
                runCatching {
                    val outputMap = mapOf(
                        "channelId" to channelId,
                        "time" to System.currentTimeMillis(),
                        "msgId" to asrResult.msgId,
                        "type" to asrResult.type,
                        "startOffset" to asrResult.startOffset,
                        "endOffset" to asrResult.endOffset,
                        "text" to asrResult.text,
                        "speakId" to asrResult.speakId
                    )
                    val outputJson = gson.toJson(outputMap)
                    originAsrOutput {
                        println(outputJson)
                        flush()
                    }
                }.onFailure {
                    DebugUtil.e(TAG, "writeOriginAsr: error: $it")
                }
            }
        }

        fun writeFilterAsr(channelId: String?, msgId: String, asrItem: ConvertContentItem) {
            synchronized(this) { if (isReleased) return }
            contentExportThread.execute {
                synchronized(this) { if (isReleased) return@execute }
                runCatching {
                    val outputMap = mapOf(
                        "channelId" to channelId,
                        "time" to System.currentTimeMillis(),
                        "msgId" to msgId,
                        "vadType" to asrItem.vadType,
                        "startOffset" to asrItem.startTime,
                        "endOffset" to asrItem.endTime,
                        "text" to asrItem.textContent,
                        "roleId" to asrItem.roleId,
                        "asrLanguage" to asrItem.asrLanguage
                    )
                    val outputJson = gson.toJson(outputMap)
                    filterAsrOutput {
                        println(outputJson)
                        flush()
                    }
                }.onFailure {
                    DebugUtil.e(TAG, "writeFilterAsr: error: $it")
                }
            }
        }

        fun writeMergedAsr(asrItem: ConvertContentItem) {
            synchronized(this) { if (isReleased) return }
            // 创建数据快照
            val snapshot = asrItem.copy()
            contentExportThread.execute {
                synchronized(this) { if (isReleased) return@execute }
                runCatching {
                    val outputMap = mapOf(
                        "id" to snapshot.id,
                        "time" to System.currentTimeMillis(),
                        "vadType" to snapshot.vadType,
                        "startOffset" to snapshot.startTime,
                        "endOffset" to snapshot.endTime,
                        "text" to snapshot.textContent,
                        "roleId" to snapshot.roleId,
                        "asrLanguage" to snapshot.asrLanguage,
                        "textOptimizationState" to snapshot.textOptimizationState.value
                    )
                    val outputJson = gson.toJson(outputMap)
                    mergedAsrOutput {
                        println(outputJson)
                        flush()
                    }
                }.onFailure {
                    DebugUtil.e(TAG, "writeMergedAsr: error: $it")
                }
            }
        }

        fun writeLlmAsr(llmUpdateReport: Map<String, Any?>) {
            synchronized(this) { if (isReleased) return }
            contentExportThread.execute {
                synchronized(this) { if (isReleased) return@execute }
                runCatching {
                    val outputJson = gson.toJson(llmUpdateReport)
                    llmAsrOutput {
                        println(outputJson)
                        flush()
                    }
                }.onFailure {
                    DebugUtil.e(TAG, "writeFilterAsr: error: $it")
                }
            }
        }

        @Synchronized
        internal fun release() {
            if (isReleased) return
            isReleased = true
            DebugUtil.d(TAG, "release: exportRoot=${exportRoot.absolutePath}")
            contentExportThread.execute { closeOutputs() }
        }

        @WorkerThread
        private fun closeOutputs() {
            pcmExportThread.clearQueue()
            pcmExportThread.quitThread()
            closeAudioPcmOutput()
            closeOriginAsrOutput()
            closeFilterAsrOutput()
            closeLlmAsrOutput()
            contentExportThread.shutdown()
        }

        private fun closeAudioPcmOutput() {
            synchronized(audioPcm) {
                runCatching {
                    audioPcmOutput?.run {
                        flush()
                        close()
                    }
                    audioPcmOutput = null
                }.onFailure {
                    DebugUtil.e(TAG, "release: close audioPcmOutput error: $it")
                }
            }
        }

        private fun closeOriginAsrOutput() {
            synchronized(originAsr) {
                runCatching {
                    originAsrOutput?.run {
                        flush()
                        close()
                    }
                    originAsrOutput = null
                }.onFailure {
                    DebugUtil.e(TAG, "release: close originAsrOutput error: $it")
                }
            }
        }

        private fun closeFilterAsrOutput() {
            synchronized(filterAsr) {
                runCatching {
                    filterAsrOutput?.run {
                        flush()
                        close()
                    }
                    filterAsrOutput = null
                }.onFailure {
                    DebugUtil.e(TAG, "release: close filterAsrOutput error: $it")
                }
            }
        }

        private fun closeMergeAsrOutput() {
            synchronized(mergedAsr) {
                runCatching {
                    mergedAsrOutput?.run {
                        flush()
                        close()
                    }
                    mergedAsrOutput = null
                }.onFailure {
                    DebugUtil.e(TAG, "release: close mergedAsrOutput error: $it")
                }
            }
        }

        private fun closeLlmAsrOutput() {
            synchronized(llmAsr) {
                runCatching {
                    llmAsrOutput?.run {
                        flush()
                        close()
                    }
                    llmAsrOutput = null
                }.onFailure {
                    DebugUtil.e(TAG, "release: close llmAsrOutput error: $it")
                }
            }
        }
    }
}