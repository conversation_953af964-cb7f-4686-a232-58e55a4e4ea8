/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** File        : LlmUpdateManager.kt
 ** Description : LlmUpdateManager.kt
 ** Version     : 1.0
 ** Date        : 2025/08/01
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2025/08/01      1.0      create
 ***********************************************************************/
package com.soundrecorder.translate.subtitle

import android.content.Context
import androidx.annotation.VisibleForTesting
import com.oplus.aiunit.textgenerate.client.TextGenerateClient
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.LanguageUtil
import com.soundrecorder.common.R
import com.soundrecorder.translate.AIAsrManager
import com.soundrecorder.translate.subtitle.llm.LlmLanguageChecker
import com.soundrecorder.translate.subtitle.llm.LlmUpdateTask
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.util.Collections
import java.util.Locale
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentLinkedQueue

class LlmUpdateManager @VisibleForTesting internal constructor(
    private val context: Context,
    private val llmMaxHistorySize: Int,
    private val resultCallback: IOnLlmUpdateResult,
    private val callbackScope: CoroutineScope?
) : LlmUpdateTask.IOnTaskResultCallback {
    companion object {
        private const val TAG = "LlmUpdateManager"
        private const val MAX_RUNNING_REQUEST = 5
        private const val UNAVAILABLE_ROLE = "unavailable"

        @JvmStatic
        fun create(
            context: Context,
            llmMaxHistorySize: Int,
            resultCallback: IOnLlmUpdateResult,
            callbackScope: CoroutineScope?
        ): LlmUpdateManager = LlmUpdateManager(
            context = context,
            llmMaxHistorySize = llmMaxHistorySize,
            resultCallback = resultCallback,
            callbackScope = callbackScope
        )
    }

    private val llmRoleCache = ConcurrentHashMap<Int, String>()
    private val updateRecords = mutableMapOf<String, UpdateRecord>()
    private val runningTasks = Collections.synchronizedSet(mutableSetOf<LlmUpdateTask>())
    private val waitingTasks = ConcurrentLinkedQueue<LlmUpdateTask>()

    internal val aiClient: TextGenerateClient by lazy { TextGenerateClient(context, context.packageName) }
    internal val languageChecker: LlmLanguageChecker by lazy { LlmLanguageChecker(context) }

    private val isAiTextSupported by lazy {
        val detect = AIAsrManager.DETECT_AI_TEXT
        AIAsrManager.isDetectSupported(context, detect).also {
            DebugUtil.d(TAG, "isAiTextSupported: $detect=$it")
        }
    }
    private val supportedLanguages by lazy {
        LanguageUtil.getAsrLlmSupportedLanguages().also {
            DebugUtil.d(TAG, "supportedLanguages: $it")
        }
    }

    var asrLanguageCode: String = Locale.CHINESE.language

    /**
     * 送入大模型的讲话人名称固定为默认的“讲话人 $ID”格式，而非用户自定义重命名的名称。
     * 该讲话人仅用于大模型识别处理。
     */
    private fun getLlmRoleName(roleId: Int): String = llmRoleCache.getOrPut(roleId) {
        context.getString(R.string.convert_speaker, roleId)
    }

    fun isLlmAvailable(): Boolean {
        if (!isAiTextSupported) return false
        if (supportedLanguages.isEmpty()) return true
        return supportedLanguages.contains(asrLanguageCode)
    }

    @Synchronized
    fun startRequest(llmUnitKey: String, roleId: Int, originContent: String) {
        if (updateRecords[llmUnitKey] != null) {
            return
        }
        if (!isLlmAvailable()) {
            updateRecords[llmUnitKey] = UpdateRecord(UNAVAILABLE_ROLE, originContent)
            callbackUseOrigin(llmUnitKey)
            return
        }
        val role = getLlmRoleName(roleId)
        val newRecord = UpdateRecord(role, originContent)
        val historyRecords = obtainHistoryRecords()
        val originReferences = historyRecords.map { it.role to it.originAsr }
        val llmReferences = historyRecords.filter { it.afterLlm.isNotEmpty() }.map { it.role to it.afterLlm }
        val params = LlmUpdateTask.TaskParams(
            llmUnitKey = llmUnitKey,
            targetAsrContent = role to originContent,
            originReferences = originReferences,
            llmReferences = llmReferences,
            asrLanguageCode = asrLanguageCode
        )
        LlmUpdateTask(this, params, this).also {
            newRecord.updateTask = it
            updateRecords[llmUnitKey] = newRecord
            requestTask(it)
        }
    }

    private fun obtainHistoryRecords(): List<UpdateRecord> {
        val results = mutableListOf<UpdateRecord>()
        val recordsList = updateRecords.values.toList()
        for (i in recordsList.lastIndex downTo 0) {
            val record = recordsList[i]
            results.add(0, record)
            if (results.size >= llmMaxHistorySize) return results
        }
        return results
    }

    private fun requestTask(task: LlmUpdateTask) {
        waitingTasks.offer(task)
        scheduleTaskStart()
    }

    @Synchronized
    fun updateLlmRoleName(llmUnitKey: String, roleId: Int) {
        val record = updateRecords[llmUnitKey] ?: return
        record.role = getLlmRoleName(roleId)
    }

    @Synchronized
    fun invalidRecord(llmUnitKey: String) {
        val record = updateRecords.remove(llmUnitKey) ?: return
        record.updateTask?.cancel()
    }

    @Synchronized
    private fun scheduleTaskStart() {
        if (runningTasks.size >= MAX_RUNNING_REQUEST) {
            return
        }
        var nextTask: LlmUpdateTask? = null
        do {
            nextTask = waitingTasks.poll()
        } while (nextTask != null && nextTask.isInvalid())
        nextTask ?: return
        runningTasks.add(nextTask)
        nextTask.sendRequest()
    }

    @Synchronized
    private fun scheduleTaskEnd(task: LlmUpdateTask) {
        runningTasks.remove(task)
        scheduleTaskStart()
    }

    private fun LlmUpdateTask.isInvalid(): Boolean = !updateRecords.contains(taskParams.llmUnitKey)

    override fun onTaskResult(task: LlmUpdateTask, llmResult: String) {
        scheduleTaskEnd(task)
        val llmUnitKey = task.taskParams.llmUnitKey
        synchronized(this) {
            val record = updateRecords[llmUnitKey] ?: return
            if (record.updateTask != task) return
            record.updateTask = null
            record.afterLlm = AsrRestructureHelper.fixLlmResultPunctuation(record.originAsr, llmResult)
            callbackUseResult(llmUnitKey, record.afterLlm)
        }
    }

    override fun onTaskFailure(task: LlmUpdateTask) {
        scheduleTaskEnd(task)
        val llmUnitKey = task.taskParams.llmUnitKey
        synchronized(this) {
            val record = updateRecords[llmUnitKey] ?: return
            if (record.updateTask != task) return
            record.updateTask = null
            callbackUseOrigin(llmUnitKey)
        }
    }

    private fun callbackUseResult(llmUnitKey: String, resultStr: String) {
        if (callbackScope == null) {
            // 单测环境下通过Mock使callback为空，让测试同步进行
            resultCallback.onLlmUpdateResult(llmUnitKey, resultStr)
        } else {
            callbackScope.launch { resultCallback.onLlmUpdateResult(llmUnitKey, resultStr) }
        }
    }

    private fun callbackUseOrigin(llmUnitKey: String) {
        if (callbackScope == null) {
            // 单测环境下通过Mock使callback为空，让测试同步进行
            resultCallback.onLlmUpdateResult(llmUnitKey, null)
        } else {
            callbackScope.launch { resultCallback.onLlmUpdateResult(llmUnitKey, null) }
        }
    }

    @Synchronized
    fun release() {
        waitingTasks.clear()
        runningTasks.forEach { it.cancel(true) }
        runningTasks.clear()
    }

    private data class UpdateRecord(
        var role: String,
        val originAsr: String,
        var afterLlm: String = ""
    ) {
        @Volatile
        @Transient
        var updateTask: LlmUpdateTask? = null
    }

    fun interface IOnLlmUpdateResult {
        fun onLlmUpdateResult(llmUnitKey: String, resultContent: String?)
    }
}