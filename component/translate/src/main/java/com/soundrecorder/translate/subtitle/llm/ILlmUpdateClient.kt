/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** File        : ILlmUpdateClient.kt
 ** Description : ILlmUpdateClient.kt
 ** Version     : 1.0
 ** Date        : 2025/08/01
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2025/08/01      1.0      create
 ***********************************************************************/
package com.soundrecorder.translate.subtitle.llm

import androidx.annotation.VisibleForTesting
import com.soundrecorder.translate.subtitle.LlmUpdateManager

interface ILlmUpdateClient {

    companion object {
        /**
         * 补充错误码：发生LLM优化请求时抛出异常
         */
        const val ERROR_CODE_SEND_REQUEST_ERROR = 10001

        /**
         * 补充错误码：无效的LLM优化返回结果(如isNullOrEmpty)
         */
        const val ERROR_CODE_INVALID_RESULT = 10002

        const val ERROR_CODE_CONTENT_DENY = 3000605

        @JvmStatic
        fun create(
            taskManager: LlmUpdateManager,
            responseCallback: ILlmUpdateResponse
        ): ILlmUpdateClient = AiLlmUpdateClient(taskManager, responseCallback)
    }

    fun sendRequest(requestData: LlmUpdateRequestData)

    fun cancel()

    interface ILlmUpdateResponse {
        fun onResponseSuccess(llmResult: String)
        fun onResponseFailure(error: LlmResponseError)
    }
}

sealed class AbsLlmUpdateClient(
    protected val responseCallback: ILlmUpdateClient.ILlmUpdateResponse
) : ILlmUpdateClient

/**
 * 仅用于单元测试的Dummy实现，用于快速模拟大模型优化请求的行为
 */
@VisibleForTesting
internal class DummyLlmUpdateClient(
    responseCallback: ILlmUpdateClient.ILlmUpdateResponse
) : AbsLlmUpdateClient(responseCallback) {
    override fun sendRequest(requestData: LlmUpdateRequestData) {
        // Directly return origin content since dummy
        responseCallback.onResponseSuccess(requestData.text)
    }

    override fun cancel() {
        // Do nothing since dummy
    }
}