/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** File        : SubtitleCollectionUtils.kt
 ** Description : SubtitleCollectionUtils.kt
 ** Version     : 1.0
 ** Date        : 2025/08/01
 ** Author      : ********
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ********       2025/08/01      1.0      create
 ***********************************************************************/
package com.soundrecorder.translate.util

/**
 * 针对字幕的列表数据进行优化，需要查找检查的字幕对象元素往往在列表末尾部分。
 * 因此反向查找以提升性能，尽量避免复杂度为O(n)的场景。
 */
fun <T> List<T>.findLastOrNull(predicate: (T) -> Boolean): T? {
    for (i in lastIndex downTo 0) {
        val element = this[i]
        if (predicate(element)) return element
    }
    return null
}

/**
 * 针对字幕的列表数据进行优化，倒序查找列表中连续满足条件的元素。
 * 从列表末尾开始向前查找，收集连续满足条件的元素，直到遇到不满足条件的元素为止。
 *
 * @param predicate 判断元素是否满足条件的谓词函数
 * @return 包含所有连续满足条件元素的列表，按原始顺序排列（从前往后）。
 *         如果没有满足条件的元素，返回空列表。
 */
fun <T> List<T>.findAllList(predicate: (T) -> Boolean): List<T> {
    val tempResult = mutableListOf<T>()
    for (i in lastIndex downTo 0) {
        val element = this[i]
        if (predicate(element)) {
            tempResult.add(element)
        } else {
            break
        }
    }
    return tempResult.reversed()
}