/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** File        : LlmUpdateRequestData.kt
 ** Description : LlmUpdateRequestData.kt
 ** Version     : 1.0
 ** Date        : 2025/08/01
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2025/08/01      1.0      create
 ***********************************************************************/
package com.soundrecorder.translate.subtitle.llm

import java.util.UUID

data class LlmUpdateRequestData(
    val role: String,
    val text: String,
    val orgReferences: List<String>,
    val llmReferences: List<String>,
    val language: String,
    val requestId: String = UUID.randomUUID().toString()
)