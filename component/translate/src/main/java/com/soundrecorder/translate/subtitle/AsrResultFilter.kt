/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** File        : AsrResultFilter.kt
 ** Description : AsrResultFilter.kt
 ** Version     : 1.0
 ** Date        : 2025/08/01
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2025/08/01      1.0      create
 ***********************************************************************/
package com.soundrecorder.translate.subtitle

import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.translate.asr.realtime.RealTimeAsrParser
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Collections
import java.util.concurrent.Executors
import kotlin.math.max

/**
 * 对原始ASR识别结果进行过滤，抑制识别中的ASR段落中途字数异常减少的情况。
 */
class AsrResultFilter(
    outputParagraphSize: Int,
    private val filteredListener: IOnAsrResultFiltered,
    filterLossCharRate: Float = DEF_LOSS_CHAR_RATE,
    private val lossCharBufferTime: Long = DEF_LOSS_CHAR_BUFFER_TIME
) {
    private companion object {
        private const val TAG = "AsrResultFilter"
        private const val DEF_LOSS_CHAR_RATE = 0.05f
        private const val DEF_LOSS_CHAR_BUFFER_TIME = 800L
    }

    var assertLossCharSize: Int = (outputParagraphSize * filterLossCharRate).toInt()
        private set

    /**
     * 统一onAsrResultFiltered的调用到始终在同一个线程上，即当作ASR结果文本处理的专用线程。
     * [LlmUpdateManager]中的大模型处理后返回结果的回调也在该线程上执行。
     */
    private val filterThread by lazy { Executors.newSingleThreadExecutor() }
    val filterScope: CoroutineScope by lazy { CoroutineScope(filterThread.asCoroutineDispatcher()) }
    private val resultsBuffer = mutableMapOf<String, AsrResultBuffer>()
    private val vadFinalMsgRecord = Collections.synchronizedSet(mutableSetOf<String>())

    fun configureParagraphSize(outputParagraphSize: Int, filterLossCharRate: Float = DEF_LOSS_CHAR_RATE) {
        assertLossCharSize = (outputParagraphSize * filterLossCharRate).toInt()
    }

    fun onAsrResult(channelId: String?, msgId: String, result: ConvertContentItem) {
        synchronized(vadFinalMsgRecord) {
            if (vadFinalMsgRecord.contains(msgId)) {
                DebugUtil.e(TAG, "onAsrResult: ignore to update VAD_FINAL msg $msgId")
                return
            }
            if (result.vadType == RealTimeAsrParser.ASR_RESULT_TYPE_VAD_FINAL) {
                vadFinalMsgRecord.add(msgId)
            }
        }
        // 隔离后续的ASR内容处理到专用线程上，避免onAsrResult在AiAsrKit的Binder线程上调用时造成阻塞
        filterScope.launch { handleAsrResult(channelId, msgId, result) }
    }

    private fun handleAsrResult(channelId: String?, msgId: String, result: ConvertContentItem) {
        val newContent = result.copy()
        resultsBuffer[msgId]?.also {
            it.update(newContent)
        } ?: AsrResultBuffer(channelId, msgId, newContent).also {
            resultsBuffer[msgId] = it
            it.flush()
        }
    }

    private fun callbackFlush(channelId: String?, msgId: String, result: ConvertContentItem) {
        if (result.vadType == RealTimeAsrParser.ASR_RESULT_TYPE_VAD_FINAL) {
            resultsBuffer.remove(msgId)
        }
        filteredListener.onAsrResultFiltered(channelId, msgId, result)
    }

    private inner class AsrResultBuffer(
        private val channelId: String?,
        private val msgId: String,
        @Volatile private var bufferedAsrResult: ConvertContentItem
    ) {

        @Volatile
        private var lastContentMaxLength: Int = bufferedAsrResult.textContent.length

        @Volatile
        private var flushJob: Job? = null

        fun update(newContent: ConvertContentItem) {
            val newLength = newContent.textContent.length
            val lastLength = lastContentMaxLength
            bufferedAsrResult = newContent
            lastContentMaxLength = max(newLength, lastLength)
            val isVadFinal = newContent.vadType == RealTimeAsrParser.ASR_RESULT_TYPE_VAD_FINAL
            if (isVadFinal || lastLength - newLength < assertLossCharSize) {
                flushInner(false)
            } else {
                triggerDelayFlush(newLength, lastLength)
            }
        }

        private fun triggerDelayFlush(newLength: Int, lastLength: Int) {
            if (flushJob != null) {
                // 等待恢复字数期间不再重复发起刷新Job
                return
            }
            DebugUtil.e(TAG, "onAsrResult: filter on msg $msgId, loss=$lastLength->$newLength")
            flushJob = filterScope.launch {
                delay(lossCharBufferTime)
                flushInner(true)
            }
        }

        fun flush() {
            flushInner(false)
        }

        private fun flushInner(isDelayFlush: Boolean) {
            val latestLength = bufferedAsrResult.textContent.length
            if (flushJob != null) {
                DebugUtil.e(TAG, "onAsrResult: flush filtered msg $msgId, latest=$latestLength")
            }
            if (!isDelayFlush) {
                runCatching { flushJob?.cancel() }
            }
            flushJob = null
            lastContentMaxLength = latestLength
            callbackFlush(channelId, msgId, bufferedAsrResult)
        }
    }

    fun release() {
        runCatching { filterScope.cancel() }
        runCatching { filterThread.shutdown() }
    }

    fun interface IOnAsrResultFiltered {
        fun onAsrResultFiltered(channelId: String?, msgId: String, content: ConvertContentItem)
    }
}