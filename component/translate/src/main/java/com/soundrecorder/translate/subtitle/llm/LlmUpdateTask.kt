/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** File        : LlmUpdateTask.kt
 ** Description : LlmUpdateTask.kt
 ** Version     : 1.0
 ** Date        : 2025/08/01
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2025/08/01      1.0      create
 ***********************************************************************/
package com.soundrecorder.translate.subtitle.llm

import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.translate.subtitle.LlmUpdateManager
import com.soundrecorder.translate.util.SubtitleDebugExportHelper

class LlmUpdateTask(
    taskManager: LlmUpdateManager,
    val taskParams: TaskParams,
    private val resultCallback: IOnTaskResultCallback
) : ILlmUpdateClient.ILlmUpdateResponse {
    private companion object {
        private const val TAG = "LlmUpdateTask"
    }

    private val updateClient = ILlmUpdateClient.create(taskManager, this)

    @Volatile
    var isSentRequest: Boolean = false
        private set

    @Volatile
    var isReceiveResponse: Boolean = false
        private set

    @Volatile
    var isCanceled: Boolean = false
        private set

    private var requestTime: Long = -1
    private var responseTime: Long = -1

    @Synchronized
    fun sendRequest() {
        if (isCanceled || isSentRequest) return
        isSentRequest = true
        requestTime = System.currentTimeMillis()
        updateClient.sendRequest(taskParams.requestData)
    }

    @Synchronized
    fun cancel(ignoreCallback: Boolean = false) {
        if (isCanceled || isReceiveResponse) return
        isCanceled = true
        DebugUtil.d(TAG, "cancel: ${taskParams.llmUnitKey}")
        updateClient.cancel()
        if (ignoreCallback) {
            return
        }
        callbackFailure()
    }

    @Synchronized
    override fun onResponseSuccess(llmResult: String) {
        if (isCanceled || isReceiveResponse) return
        isReceiveResponse = true
        responseTime = System.currentTimeMillis()
        DebugUtil.d(TAG, "onResponseSuccess: ${taskParams.llmUnitKey}, result.size=${llmResult.length}")
        resultCallback.onTaskResult(this, llmResult)
        onExportResponse(llmResult, null)
    }

    @Synchronized
    override fun onResponseFailure(error: LlmResponseError) {
        if (isCanceled || isReceiveResponse) return
        isReceiveResponse = true
        responseTime = System.currentTimeMillis()
        DebugUtil.e(TAG, "onResponseFailure: ${taskParams.llmUnitKey}, err=$error")
        callbackFailure()
        onExportResponse(null, error)
    }

    private fun callbackFailure() {
        resultCallback.onTaskFailure(this)
    }

    private fun onExportResponse(llmResult: String?, error: LlmResponseError?) {
        val debugExporter = SubtitleDebugExportHelper.export() ?: return
        val report = LlmUpdateReport(
            llmUnitKey = taskParams.llmUnitKey,
            requestId = taskParams.requestData.requestId,
            requestTime = requestTime,
            responseTime = responseTime,
            role = taskParams.targetAsrContent.first,
            originStr = taskParams.targetAsrContent.second,
            orgReferences = taskParams.requestData.orgReferences,
            llmReferences = taskParams.requestData.llmReferences,
            language = taskParams.requestData.language,
            resultStr = llmResult,
            errCode = error?.errorCode,
            errMsg = error?.errorMsg,
            errExpends = error?.expends
        )
        debugExporter.writeLlmAsr(report.toReportData())
    }

    data class TaskParams(
        val llmUnitKey: String,
        val targetAsrContent: Pair<String, String>,
        val originReferences: List<Pair<String, String>>,
        val llmReferences: List<Pair<String, String>>,
        val asrLanguageCode: String
    ) {
        val requestData: LlmUpdateRequestData by lazy {
            LlmUpdateRequestData(
                role = targetAsrContent.first,
                text = targetAsrContent.second,
                orgReferences = originReferences.map { (_, content) -> content },
                llmReferences = llmReferences.map { (_, content) -> content },
                language = asrLanguageCode
            )
        }
    }

    interface IOnTaskResultCallback {

        fun onTaskResult(task: LlmUpdateTask, llmResult: String)

        fun onTaskFailure(task: LlmUpdateTask)
    }
}