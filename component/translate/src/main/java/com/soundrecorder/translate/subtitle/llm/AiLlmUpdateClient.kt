/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** File        : AiLlmUpdateClient.kt
 ** Description : AiLlmUpdateClient.kt
 ** Version     : 1.0
 ** Date        : 2025/08/01
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2025/08/01      1.0      create
 ***********************************************************************/
package com.soundrecorder.translate.subtitle.llm

import com.oplus.aiunit.textgenerate.callback.TextGenerateCallback
import com.oplus.aiunit.textgenerate.client.TextGenerateClient
import com.oplus.aiunit.textgenerate.data.TextGenerateRspData
import com.oplus.aiunit.textgenerate.request.CancelRequest
import com.oplus.aiunit.textgenerate.request.TextGenerateRequest
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.translate.subtitle.LlmUpdateManager
import com.soundrecorder.translate.subtitle.llm.ILlmUpdateClient.Companion.ERROR_CODE_CONTENT_DENY
import com.soundrecorder.translate.subtitle.llm.ILlmUpdateClient.Companion.ERROR_CODE_INVALID_RESULT
import com.soundrecorder.translate.subtitle.llm.ILlmUpdateClient.Companion.ERROR_CODE_SEND_REQUEST_ERROR
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import java.util.Locale

class AiLlmUpdateClient(
    taskManager: LlmUpdateManager,
    responseCallback: ILlmUpdateClient.ILlmUpdateResponse
) : AbsLlmUpdateClient(responseCallback), TextGenerateCallback {
    private companion object {
        private const val TAG = "AiLlmUpdateClient"

        private const val LLM_UPDATE_TIMEOUT = 30000
        private const val ERROR_RESULT_DENY = "71&3"

        @JvmStatic
        private fun LlmUpdateRequestData.createAiTextRequest(
            timeout: Int = LLM_UPDATE_TIMEOUT,
            overrideLanguage: String? = null,
        ): TextGenerateRequest = TextGenerateRequest().also {
            it.requestId = requestId //请求 Id，必传，业务保证唯一性
            it.role = role // 本次顺滑文本的讲话人
            it.text = text // 本次需要顺滑的文本
            it.orgReferences = orgReferences // 历史顺滑前的文本(格式："${讲话人}:${文本内容}")
            it.references = llmReferences // 历史顺滑后的文本(格式："${讲话人}:${文本内容}")
            it.language = overrideLanguage ?: language // 语种
            it.timeout = timeout //业务侧传入的超时时长，单位 ms（可选），默认 30000ms
        }

        @JvmStatic
        private fun TextGenerateRequest.logInfo(): String =
            "requestId=$requestId, language=$language, role=$role, text=${text.length}, " +
                    "orgReferences=${orgReferences?.size}, references=${references?.size}, timeout=$timeout"
    }

    private val aiClient: TextGenerateClient = taskManager.aiClient
    private val languageChecker: LlmLanguageChecker = taskManager.languageChecker
    private val taskScope = CoroutineScope(Dispatchers.Default)

    @Volatile
    private var isCanceled = false
    private var sentRequestData: LlmUpdateRequestData? = null

    private fun getRequestId(): String? = sentRequestData?.requestId

    @Synchronized
    override fun sendRequest(requestData: LlmUpdateRequestData) {
        if (isCanceled) return
        sentRequestData = requestData
        runCatching { taskScope.launch { onSendRequest(requestData) } }
    }

    private fun onSendRequest(requestData: LlmUpdateRequestData) {
        val overrideLanguage = detectOverrideLanguage(requestData)
        val request = requestData.createAiTextRequest(overrideLanguage = overrideLanguage)
        DebugUtil.d(TAG, "sendRequest: ${request.logInfo()}")
        runCatching {
            aiClient.createTextGenerate(request, this)
        }.onFailure {
            val errMsg = it.toString()
            DebugUtil.e(TAG, "sendRequest: ERROR! $errMsg")
            responseCallback.onResponseFailure(LlmResponseError(ERROR_CODE_SEND_REQUEST_ERROR, errMsg))
        }
    }

    /**
     * 多数非英语语种的ASR均具备一定的兼容识别英语内容的能力，而大模型优化原则上不能翻译。
     * 故指定语种非英语的情况下，针对大概率是英文的内容，请求大模型是按英文请求。
     */
    private fun detectOverrideLanguage(requestData: LlmUpdateRequestData): String? {
        if (requestData.language == Locale.ENGLISH.language) {
            return null
        }
        if (languageChecker.isProbablyEnglish(requestData.text)) {
            DebugUtil.d(TAG, "onSendRequest: requestId=${requestData.requestId}, text is probably english.")
            return Locale.ENGLISH.language
        }
        return null
    }

    @Synchronized
    override fun cancel() {
        if (isCanceled) return
        isCanceled = true
        DebugUtil.d(TAG, "cancel: requestId=${getRequestId()}")
        taskScope.cancel()
        val requestId = getRequestId() ?: return
        val cancelRequest = CancelRequest().also {
            it.requestId = requestId
        }
        runCatching {
            aiClient.cancelTextGenerate(cancelRequest)
        }.onFailure {
            DebugUtil.e(TAG, "cancel: ERROR! $it")
        }
    }

    @Synchronized
    override fun onResult(result: TextGenerateRspData?) {
        if (isCanceled) return
        DebugUtil.d(TAG, "onResult: requestId=${getRequestId()}, resContent=${result?.resContent?.length}")
        // 防止阻塞AiSDK的Binder线程
        runCatching { taskScope.launch { onSdkResults(result) } }
    }

    private fun onSdkResults(result: TextGenerateRspData?) {
        val resultStr = result?.resContent
        if (resultStr.isNullOrEmpty()) {
            responseCallback.onResponseFailure(LlmResponseError(ERROR_CODE_INVALID_RESULT, "resContent isNullOrEmpty"))
        } else if (resultStr.trim() == ERROR_RESULT_DENY) {
            // 临时措施，云侧未正确识别大模型的拒识标志位并作为错误信息及错误码返回，单独识别处理
            responseCallback.onResponseFailure(LlmResponseError(ERROR_CODE_CONTENT_DENY, "LLM deny"))
        } else {
            responseCallback.onResponseSuccess(resultStr)
        }
    }

    /**
     * 错误码参考：
     * https://odocs.myoas.com/docs/m5kv9bQ1Y8fyKQqX/ 《AI Kit  错误码》访问密码 ukpxhv
     */
    @Synchronized
    override fun onError(errorCode: Int, errorMsg: String?, expends: String?) {
        if (isCanceled) return
        DebugUtil.e(TAG, "onError: requestId=${getRequestId()}, errorCode=$errorCode, errorMsg=$errorMsg, expends=$expends")
        // 防止阻塞AiSDK的Binder线程
        runCatching { taskScope.launch { onSdkError(errorCode, errorMsg, expends) } }
    }

    private fun onSdkError(errorCode: Int, errorMsg: String?, expends: String?) {
        responseCallback.onResponseFailure(LlmResponseError(errorCode, errorMsg, expends))
    }
}