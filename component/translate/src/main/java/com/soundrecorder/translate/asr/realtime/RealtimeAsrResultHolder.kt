/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - RealtimeAsrResultHolder.kt
 * Description:
 *     Optimize accessing with [LinkedHashMap] for ASRResult.
 *
 * Version: 1.0
 * Date: 2025-07-19
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * Bixia<PERSON>.<EMAIL>    2025-07-19   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.translate.asr.realtime

import com.soundrecorder.common.databean.ConvertContentItem
import java.util.Collections

class RealtimeAsrResultHolder {
    private val asrResultMap = mutableMapOf<String, ConvertContentItem>()
    private val asrResultList = Collections.synchronizedList(mutableListOf<ConvertContentItem>())

    @Volatile
    private var lastEntry: HolderEntry? = null

    @Synchronized
    operator fun set(msgId: String, asrResult: ConvertContentItem) {
        val oldResult = asrResultMap[msgId]
        asrResultMap[msgId] = asrResult
        if (oldResult == null) {
            asrResultList.add(asrResult)
            lastEntry = HolderEntry(msgId to asrResult)
        } else {
            reverseFindAndReplace(oldResult, asrResult)
            if (lastEntry?.key == msgId) {
                lastEntry = HolderEntry(msgId to asrResult)
            }
        }
    }

    @Synchronized
    fun getKeys(): MutableSet<String> = asrResultMap.keys

    @Synchronized
    operator fun get(msgId: String): ConvertContentItem? = asrResultMap[msgId]

    @Synchronized
    fun values(): List<ConvertContentItem> = asrResultList

    @Synchronized
    fun lastOrNull(): Map.Entry<String, ConvertContentItem>? = lastEntry

    @Synchronized
    fun clear() {
        asrResultMap.clear()
        asrResultList.clear()
        lastEntry = null
    }

    private fun reverseFindAndReplace(oldResult: ConvertContentItem, newResult: ConvertContentItem) {
        for (i in asrResultList.lastIndex downTo 0) {
            val find = asrResultList[i]
            if (find === oldResult) {
                asrResultList[i] = newResult
                break
            }
        }
    }

    private class HolderEntry(
        private val entry: Pair<String, ConvertContentItem>
    ) : Map.Entry<String, ConvertContentItem> {
        override val key: String
            get() = entry.first
        override val value: ConvertContentItem
            get() = entry.second
    }
}