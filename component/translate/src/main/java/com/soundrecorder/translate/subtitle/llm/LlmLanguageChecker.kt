/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** File        : LlmLanguageChecker.kt
 ** Description : LlmLanguageChecker.kt
 ** Version     : 1.0
 ** Date        : 2025/08/01
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2025/08/01      1.0      create
 ***********************************************************************/
package com.soundrecorder.translate.subtitle.llm

import android.content.Context
import android.view.textclassifier.TextClassificationManager
import android.view.textclassifier.TextLanguage
import com.soundrecorder.base.utils.DebugUtil
import java.util.Locale

class LlmLanguageChecker(context: Context) {
    companion object {
        private const val TAG = "LlmLanguageChecker"
        private const val ENABLE_DEBUG_LOG = false

        @JvmStatic
        private fun debugLog(log: () -> String) {
            if (ENABLE_DEBUG_LOG) {
                DebugUtil.d(TAG, log())
            }
        }
    }

    private val textClassifier = context.also {
        debugLog { "textClassifier: start obtain" }
    }.getSystemService(TextClassificationManager::class.java)?.textClassifier.also {
        debugLog { "textClassifier: end obtain, get=$it" }
    }

    fun isProbablyEnglish(text: String): Boolean {
        textClassifier ?: return false
        debugLog { "isProbablyEnglish: start, text=$text" }
        val request = TextLanguage.Request.Builder(text).build()
        /* textClassifier首次调用一般有额外的100ms左右的初始化耗时，后续调用根据字数一般耗时数毫秒到数十毫秒。 */
        val probablyLang = findMaxScoreLanguage(textClassifier.detectLanguage(request)) ?: return false
        debugLog { "isProbablyEnglish: end, probablyLang=$probablyLang" }
        return probablyLang == Locale.ENGLISH.language
    }

    private fun findMaxScoreLanguage(detectResult: TextLanguage): String? {
        var detectLang: String? = null
        var detectScore = -1f
        for (i in 0 until detectResult.localeHypothesisCount) {
            val currLocal = detectResult.getLocale(i)
            val currScore = detectResult.getConfidenceScore(currLocal)
            if (currScore <= detectScore) {
                continue
            }
            detectScore = currScore
            detectLang = currLocal.language
        }
        return detectLang
    }
}