/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RealtimeConvertManager
 * Description:
 * Version: 1.0
 * Date: 2025/3/6
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2025/3/6 1.0 create
 */

package com.soundrecorder.translate.asr

import android.os.Bundle
import android.os.SystemClock
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.AIErrorBuryingPoint
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.realtimeasr.IRealtimeSubtitleCache
import com.soundrecorder.common.realtimeasr.OnRealtimeListener
import com.soundrecorder.common.realtimeasr.RealTimeAsrStatus
import com.soundrecorder.common.realtimeasr.RealTimeAsrStatusConst
import com.soundrecorder.translate.asr.bean.AsrResult
import com.soundrecorder.translate.asr.listener.IRealTimeAsrListener
import com.soundrecorder.translate.asr.realtime.AIRealTimeAsrClient
import com.soundrecorder.translate.asr.realtime.AIRealTimeAsrClient.Companion.EXTRA_SOURCE_LANGUAGE
import com.soundrecorder.translate.asr.realtime.RealTimeAsrParser
import com.soundrecorder.translate.asr.realtime.RealTimeAsrParser.Companion.STATUS_NET_CONNECTED
import com.soundrecorder.translate.asr.realtime.RealtimeAsrResultHolder
import com.soundrecorder.translate.subtitle.AsrContentManager
import com.soundrecorder.translate.util.SubtitleDebugExportHelper
import com.soundrecorder.translate.util.findLastOrNull
import java.util.Locale
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentMap

class RealtimeAsrManager {

    companion object {
        private const val TAG = "RealtimeAsrManager"

        private const val DEFAULT_SPEAKER_ID = 1
        private const val UNSUPPORTED_SPEAKER_ID = -1
        private const val PROCESS_SPEAKER_ID = 0
        private const val DELAY_UPDATE_TIME = 800L
        private const val UNDERLINE = "_"
    }

    private var asrClient: AIRealTimeAsrClient? = null
    private var listener: IRealTimeAsrListener? = null
    private val asrResultHolder = RealtimeAsrResultHolder()

    var asrCallback: RealtimeAsrCallback? = null
    var offsetManager: RealtimeOffsetManager? = null

    /*监听ASR结果*/
    private var rtAsrListener: OnRealtimeListener? = null

    /*记录startAsr的成功结果、调用stop后，该值修改为false*/
    private val startAsrResultMap: ConcurrentMap<String, Boolean> = ConcurrentHashMap()

    /*记录initAsr结果，true：成功 false：失败*/
    private val initAsrResultMap: ConcurrentMap<String, Boolean> = ConcurrentHashMap()

    /*记录调用stopAsr，true：已经调用过了，false：还未调用*/
    private val stopAsrFlagMap: ConcurrentMap<String, Boolean> = ConcurrentHashMap()

    private var isDestroy = false

    @Volatile
    var isError = false

    /*ASR的当前状态*/
    private var asrStatus: RealTimeAsrStatus = RealTimeAsrStatus.ASR_DEFAULT

    /*讲话人ID到名字的映射表*/
    private val speakerIdToNameMap = ConcurrentHashMap<Int, String>()

    private var asrContentMgr: AsrContentManager? = null

    /*记录上一个最终态ASR返回的原始数据*/
    private var lastFinalAsrResult: AsrResult? = null

    init {
        runCatching {
            val startTime = SystemClock.elapsedRealtime()
            asrClient = AIRealTimeAsrClient(BaseApplication.getAppContext())
            DebugUtil.d(TAG, "new client spend ${SystemClock.elapsedRealtime() - startTime}")
        }.onFailure {
            DebugUtil.e(TAG, "init error $it")
        }
        listener = object : IRealTimeAsrListener {
            override fun onStatus(channelId: String?, bizType: String?, code: Int?, msg: String?) {
                /**流程结束，忽略状态回调*/
                if (isDestroy) {
                    DebugUtil.i(TAG, "on status return destory")
                    return
                }

                when (bizType) {
                    RealTimeAsrParser.BIZ_TYPE_START_ASK -> {
                        if (code == 0) { // startAsr 成功回调
                            channelId?.let {
                                startAsrResultMap[it] = true
                                asrStatus = RealTimeAsrStatus.ASR_RUNNING
                            }
                            return
                        }
                    }

                    RealTimeAsrParser.BIZ_TYPE_END_ASK -> {
                        asrCallback?.onStopAsrResult(code == 0, channelId)
                        if (code == 0) { // stopAsr 成功回调
                            return
                        }
                    }

                    RealTimeAsrParser.BIZ_TYPE_AUDIO -> { // 发送音频数据错误
                        if (code == RealTimeAsrParser.ERROR_NOT_FOUND_CHANNEL_ID && stopAsrFlagMap[channelId] == true) {
                            /*忽略发送数据，通道被关闭的场景*/
                            return
                        }
                    }

                    RealTimeAsrParser.BIZ_TYPE_SERVER_END_ASK -> { // 服务端主动断开链路标志
                        DebugUtil.w(TAG, "on status server end")
                        channelId?.let {
                            if (startAsrResultMap[it] == true) {
                                startAsrResultMap[it] = false
                            }
                        }
                        asrStatus = RealTimeAsrStatus.ASR_DEFAULT
                        isError = true
                        asrCallback?.onError(channelId, code, msg)
                        rtAsrListener?.onAsrStatus(RealTimeAsrStatusConst.STATUS_SERVER_DISCONNECT)
                        return
                    }
                }

                when (code) {
                    RealTimeAsrParser.STATUS_INIT_SUCCESS -> {
                        initAsrResultMap[channelId] = true
                        asrStatus = RealTimeAsrStatus.ASR_INITIALIZING
                        asrCallback?.onInitResult(channelId, true)
                        rtAsrListener?.onAsrStatus(code)
                        return
                    }

                    RealTimeAsrParser.STATUS_INIT_ERROR -> {
                        initAsrResultMap[channelId] = false
                        asrStatus = RealTimeAsrStatus.ASR_DEFAULT
                        asrCallback?.onInitResult(channelId, false)
                        AIErrorBuryingPoint.addAIAsrRealtimeErrorMsg(channelId, AIErrorBuryingPoint.VALUE_ERR_REAL_TIME_INIT_ASR,
                            code, msg, null)
                        rtAsrListener?.onAsrStatus(code)
                        return
                    }
                    /**网络已连接*/
                    STATUS_NET_CONNECTED -> return
                    else -> {
                        if (channelId?.isNotBlank() == true && stopAsrFlagMap[channelId] == true) {
                            DebugUtil.d(TAG, "ignore error by stop")
                            return
                        }
                        isError = true
                        asrStatus = RealTimeAsrStatus.ASR_DEFAULT
                        asrCallback?.onError(channelId, code, msg)
                        rtAsrListener?.onAsrStatus(code ?: -1)
                    }
                }
            }

            override fun onAsrResult(channelId: String?, asrResult: AsrResult) {
                SubtitleDebugExportHelper.export()?.writeOriginAsr(channelId, asrResult)
                val msgID = asrResult.msgId ?: return
                val realMsgID = generateMsgId(msgID, asrResult)
                val emptyOffset = offsetManager?.emptyOffsetTime ?: 0
                val mainRecordingTime = offsetManager?.mainRecordingTime ?: 0
                val realOffset = mainRecordingTime - emptyOffset
                if (asrResultHolder[realMsgID] == null) {
                    // 当下一句出现的时候，无论因为什么原因导致上一句仍为中间句，都需要把上一个结果设置为最终句。
                    asrResultHolder.lastOrNull()?.let {
                        if (it.value.vadType != RealTimeAsrParser.ASR_RESULT_TYPE_VAD_FINAL) {
                            it.value.vadType = RealTimeAsrParser.ASR_RESULT_TYPE_VAD_FINAL
                            asrContentMgr?.onAsrResult(it.value.asrChannelId, it.key, it.value)
                        }
                    }
                    asrResultHolder[realMsgID] = ConvertContentItem().apply {
                        startTime = asrResult.startOffset.toLong() + realOffset
                        endTime = asrResult.endOffset.toLong() + realOffset
                        textContent = asrResult.text
                        vadType = asrResult.type
                        roleId = generateSpeakerIdWithConditions(asrResult.speakId)
                        roleName = getRoleNameByRoleId(roleId)
                        asrChannelId = channelId
                    }.also {
                        asrContentMgr?.onAsrResult(channelId, realMsgID, it)
                    }
                } else {
                    asrResultHolder[realMsgID]?.apply {
                        startTime = asrResult.startOffset.toLong() + realOffset
                        endTime = asrResult.endOffset.toLong() + realOffset
                        textContent = asrResult.text
                        vadType = asrResult.type
                        roleId = generateSpeakerIdWithConditions(asrResult.speakId)
                        roleName = getRoleNameByRoleId(roleId)
                        asrChannelId = channelId
                        asrContentMgr?.onAsrResult(channelId, realMsgID, this@apply)
                    }
                }
            }

            override fun onTranslationCfgError(channelId: String?, errorCode: Int, errorMsg: String?, isInnerInInvoke: Boolean) {
                rtAsrListener?.onTranslationCfgError(errorCode, errorMsg)
                DebugUtil.d(TAG, "onTranslationCfgError rtAsrListener=$rtAsrListener")
                if (isInnerInInvoke) {
                    asrCallback?.onTranslationCfgError(channelId, errorCode, errorMsg)
                }
            }

            override fun onTranslationCfgSuccess(channelId: String?, parseData: Map<String, String>, isInnerInInvoke: Boolean) {
                rtAsrListener?.onTranslationCfgSuccess(parseData)
                DebugUtil.d(TAG, "onTranslationCfgSuccess rtAsrListener=$rtAsrListener")
                if (isInnerInInvoke) {
                    asrCallback?.onTranslationCfgSuccess(channelId, parseData)
                }
            }
        }
        listener?.let {
            asrClient?.registerAsrListener(it)
        }
        asrContentMgr = AsrContentManager(
            context = BaseApplication.getAppContext(),
            subtitleListenerGetter = { rtAsrListener }
        )
    }

    /**
     * 规避微软问题， 同一个msgId返回了两次VAD_FINAL，且时间戳、内容和讲话人ID都不一样。
     *
     * @param asrResult ASR返回的原始数据
     * @return 处理后的MsgId
     */
    private fun generateMsgId(originMsgId: String, asrResult: AsrResult): String {
        var newMsgId = originMsgId
        lastFinalAsrResult?.let { lastFinalAsrResult ->
            val lastFinalMsgId = lastFinalAsrResult.msgId ?: return@let
            if (originMsgId == lastFinalMsgId && asrResult.type == RealTimeAsrParser.ASR_RESULT_TYPE_VAD_FINAL) {
                val lastFinalSpeakerId = lastFinalAsrResult.speakId ?: return@let
                val currentFinalSpeakId = asrResult.speakId ?: return@let
                if (lastFinalSpeakerId != currentFinalSpeakId && lastFinalAsrResult.startOffset != asrResult.startOffset) {
                    // 计算已经存在的相同msgId的数量，例如 125_1  125_2
                    val existingCount = asrResultHolder.getKeys().count { it.startsWith("$originMsgId$UNDERLINE") }
                    // 生成新的后缀编号
                    val suffixNumber = existingCount + 1
                    newMsgId = "$originMsgId$UNDERLINE$suffixNumber"
                }
            }
        }
        if (asrResult.type == RealTimeAsrParser.ASR_RESULT_TYPE_VAD_FINAL) {
            lastFinalAsrResult = asrResult
        }
        return newMsgId
    }

    fun initAsr(param: Bundle?) {
        DebugUtil.i(TAG, "init asr")
        runCatching {
            asrStatus = RealTimeAsrStatus.ASR_INITIALIZING
            asrClient?.initAsr(param)
        }.onFailure {
            DebugUtil.e(TAG, "initAsr error $it")
            asrStatus = RealTimeAsrStatus.ASR_DEFAULT
        }
    }

    fun startAsr(channelId: String, param: Bundle?) {
        /*已经调用过stopAsr了,避免init回来之前用户点击了暂停*/
        if (stopAsrFlagMap[channelId] == true) {
            DebugUtil.w(TAG, "startAsr return by stop")
            return
        }
        runCatching {
            // 将当前的转写的语种给到数据合并类
            val sourceLanguage = param?.getString(EXTRA_SOURCE_LANGUAGE) ?: Locale.CHINESE.language
            asrContentMgr?.configureAsrLanguage(channelId, sourceLanguage)
            asrStatus = RealTimeAsrStatus.ASR_STARTING
            asrClient?.startAsr(channelId, param)
        }.onFailure {
            DebugUtil.e(TAG, "startAsr error $it")
            asrStatus = RealTimeAsrStatus.ASR_DEFAULT
        }
    }

    fun getStartAsrResult(channelId: String): Boolean {
        return startAsrResultMap[channelId] ?: false
    }

    fun stopAsr(channelId: String) {
        /*已经调用过stopAsr了*/
        if (stopAsrFlagMap[channelId] == true) {
            return
        }
        runCatching {
            asrStatus = RealTimeAsrStatus.ASR_STOPPING
            asrClient?.stopAsr(channelId)
            stopAsrFlagMap[channelId] = true
            startAsrResultMap[channelId] = false
        }.onFailure {
            DebugUtil.e(TAG, "stopAsr error $it")
            asrStatus = RealTimeAsrStatus.ASR_DEFAULT
        }
    }

    fun processAudioData(channelId: String, data: ByteArray?) {
        runCatching {
            asrClient?.processRealTimeData(channelId, data)
        }.onFailure {
            DebugUtil.e(TAG, "processAudioData error $it")
        }
    }

    fun releaseChannel(channelId: String) {
        runCatching {
            asrStatus = RealTimeAsrStatus.ASR_RELEASING
            asrClient?.releaseChannel(channelId)
        }.onFailure {
            DebugUtil.e(TAG, "release error $it")
            asrStatus = RealTimeAsrStatus.ASR_DEFAULT
        }
    }

    fun release() {
        isDestroy = true
        runCatching {
            asrStatus = RealTimeAsrStatus.ASR_RELEASING
            asrClient?.release()
        }.onFailure {
            DebugUtil.e(TAG, "release error $it")
            asrStatus = RealTimeAsrStatus.ASR_DEFAULT
        }
        listener = null
        asrClient = null
        rtAsrListener = null
        asrContentMgr?.release()
        asrContentMgr = null
        offsetManager = null
        asrResultHolder.clear()
        speakerIdToNameMap.clear()
    }

    fun isStopAsr(channelId: String): Boolean {
        return stopAsrFlagMap[channelId] ?: false
    }

    fun getAsrContent(isMergedData: Boolean = true): List<ConvertContentItem> {
        var results: List<ConvertContentItem>? = null
        if (isMergedData) {
            results = asrContentMgr?.run { getRealtimeSubtitleCache().getCurrentAllSubtitles() }
        }
        return results ?: asrResultHolder.values()
    }

    fun registerRtAsrListener(listener: OnRealtimeListener) {
        rtAsrListener = listener
        DebugUtil.d(TAG, "registerRtAsrListener rtAsrListener=$rtAsrListener")
    }

    fun unregisterRtAsrListener(listener: OnRealtimeListener) {
        if (rtAsrListener == listener) {
            rtAsrListener = null
            DebugUtil.d(TAG, "unregisterRtAsrListener rtAsrListener cleared")
        }
    }

    /**
     * 获取ASR支持的语种
     */
    fun getTranslationConfig(channelId: String, isInnerInInvoke: Boolean = false) {
        asrClient?.getTranslationConfig(channelId, isInnerInInvoke)
    }

    /**
     * 获取ASR当前的状态
     */
    fun getRealtimeAsrStatus(): RealTimeAsrStatus {
        return asrStatus
    }

    /**
     * 修改讲话人名字
     * @param roleId 讲话人ID
     * @param newName 新的讲话人名字
     * @return 是否修改成功
     */
    fun updateSpeakerName(roleId: Int, newName: String): Boolean {
        if (newName.isBlank()) return false
        speakerIdToNameMap[roleId] = newName
        DebugUtil.i(TAG, "Updated speaker name: roleId=$roleId, newName=$newName")
        asrContentMgr?.updateRoleName(roleId, newName)
        return true
    }

    /**
     * 获取ASR缓存对象
     */
    fun getRealtimeSubtitleCache(): IRealtimeSubtitleCache? {
        return asrContentMgr?.getRealtimeSubtitleCache()
    }

    /**
     * 根据实时ASR识别状态生成讲话人id
     *
     * 1. 如果originalRoleId为空，则返回-1,表示不支持讲话人功能
     * 2. 如果originalRoleId等于PROCESS_SPEAKER_ID，则返回前一句话的讲话人ID,表示正在处理，后续识别完成后会修正
     * 3. 其他情况，表示识别完成的讲话人id，不做处理，直接返回即可
     *
     * @param originalRoleId 原始的讲话人ID
     * @return 生成的讲话人ID
     */
    private fun generateSpeakerIdWithConditions(originalRoleId: Int?): Int = when (originalRoleId) {
        null -> UNSUPPORTED_SPEAKER_ID
        PROCESS_SPEAKER_ID -> {
            getAsrContent(false).findLastOrNull { it.vadType == RealTimeAsrParser.ASR_RESULT_TYPE_VAD_FINAL }?.roleId
                ?: DEFAULT_SPEAKER_ID
        }

        else -> originalRoleId
    }

    /**
     * 根据讲话人ID获取讲话人名字
     *
     * 1. 如果id为-1，则表示不支持讲话人功能，则返回Null
     * 2. 如果id为0，则尝试从rtAsrCache中获取最后一个生成的讲话人名字，如果不存在，则使用DEFAULT_SPEAKER_ID生成默认名字
     * 3. 对于其他id，查询是否存在讲话人名字，如果有则返回，如果没有则使用讲话人ID生成默认名字
     *
     * @param roleId 讲话人ID
     * @return 讲话人名字，如果不支持则返回null
     */
    private fun getRoleNameByRoleId(roleId: Int): String? = when (roleId) {
        UNSUPPORTED_SPEAKER_ID -> null
        PROCESS_SPEAKER_ID -> {
            getAsrContent(false).findLastOrNull { it.vadType == RealTimeAsrParser.ASR_RESULT_TYPE_VAD_FINAL }?.roleName
                ?: genRoleNameByRoleId(DEFAULT_SPEAKER_ID)
        }

        else -> speakerIdToNameMap[roleId] ?: genRoleNameByRoleId(roleId)
    }

    /**
     * 根据id生成默认的名字，格式如 讲话人 %d
     */
    private fun genRoleNameByRoleId(roleId: Int): String {
        val text = BaseApplication.getAppContext().getString(com.soundrecorder.common.R.string.convert_speaker, roleId)
        return text
    }
}

interface RealtimeAsrCallback {

    fun onInitResult(channelId: String?, success: Boolean)
    fun onError(channelId: String?, code: Int?, msg: String?)

    fun onStopAsrResult(result: Boolean, channelId: String?)

    fun onTranslationCfgError(channelId: String?, errorCode: Int, errorMsg: String?)

    fun onTranslationCfgSuccess(channelId: String?, data: Map<String, String>)
}

