/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** File        : AsrFinalContentCache.kt
 ** Description : AsrFinalContentCache.kt
 ** Version     : 1.0
 ** Date        : 2025/08/01
 ** Author      : ********
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ********       2025/08/01      1.0      create
 ***********************************************************************/
package com.soundrecorder.translate.subtitle.cache

import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.realtimeasr.IRealtimeSubtitleCache
import com.soundrecorder.translate.asr.realtime.RealTimeAsrParser
import com.soundrecorder.translate.util.findAllList
import com.soundrecorder.translate.util.findLastOrNull
import java.util.Collections

class AsrFinalContentCache : IRealtimeSubtitleCache {

    private val currentSubtitles = Collections.synchronizedList(mutableListOf<ConvertContentItem>())

    @Volatile
    private var nextId = 0 // 用于生成递增ID

    @Synchronized
    internal fun updateRoleName(roleId: Int, roleName: String) {
        currentSubtitles.forEach {
            if (it.roleId == roleId) {
                it.roleName = roleName
            }
        }
    }

    @Synchronized
    internal fun isEmpty(): Boolean = currentSubtitles.isEmpty()

    @Synchronized
    internal fun putItem(item: ConvertContentItem) {
        if (item.id.isEmpty()) {
            item.id = generateUniqueId()
        }
        currentSubtitles.add(item)
    }

    @Synchronized
    internal fun lastOrNullItem(): ConvertContentItem? {
        return currentSubtitles.lastOrNull()
    }

    @Synchronized
    internal fun findIntermediateItem(): ConvertContentItem? {
        return currentSubtitles.findLastOrNull { it.vadType == RealTimeAsrParser.ASR_RESULT_TYPE_INTERMEDIATE }
    }

    @Synchronized
    internal fun findNeedOptimizedItems(): List<ConvertContentItem> {
        return currentSubtitles.findAllList {
            it.vadType == RealTimeAsrParser.ASR_RESULT_TYPE_VAD_FINAL &&
            it.textOptimizationState == ConvertContentItem.TextOptimizationState.UNPROCESSED
        }
    }

    @Synchronized
    internal fun findItemById(itemId: String): ConvertContentItem? = currentSubtitles.findLastOrNull { it.id == itemId }

    /**
     * 生成唯一ID
     */
    private fun generateUniqueId(): String {
        return (nextId++).toString()
    }

    @Synchronized
    override fun getGeneratedSubtitles(): List<ConvertContentItem> {
        return currentSubtitles.filter { it.vadType == RealTimeAsrParser.ASR_RESULT_TYPE_VAD_FINAL }
    }

    @Synchronized
    override fun getTemporySubtitles(): List<ConvertContentItem> {
        return currentSubtitles.filter { it.vadType == RealTimeAsrParser.ASR_RESULT_TYPE_INTERMEDIATE }
    }

    @Synchronized
    override fun getCurrentAllSubtitles(): List<ConvertContentItem> = currentSubtitles
}