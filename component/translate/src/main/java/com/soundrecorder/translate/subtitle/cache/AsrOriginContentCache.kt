/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** File        : AsrOriginContentCache.kt
 ** Description : AsrOriginContentCache.kt
 ** Version     : 1.0
 ** Date        : 2025/08/07
 ** Author      : ********
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ********       2025/08/07      1.0      create
 ***********************************************************************/
package com.soundrecorder.translate.subtitle.cache

import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.translate.util.findAllList
import java.util.Collections

class AsrOriginContentCache {
    private val contentCacheMap = mutableMapOf<String, OriginAsrContentUnit>()
    private val contentCacheList = Collections.synchronizedList(mutableListOf<OriginAsrContentUnit>())

    @Synchronized
    operator fun set(msgId: String, originUnit: OriginAsrContentUnit) {
        val oldResult = contentCacheMap[msgId]
        contentCacheMap[msgId] = originUnit
        if (oldResult == null) {
            contentCacheList.add(originUnit)
        } else {
            reverseFindAndReplace(oldResult, originUnit)
        }
    }
    @Synchronized
    internal fun updateRoleName(roleId: Int, roleName: String) {
        contentCacheMap.forEach { _, v ->
            if (v.originContent.roleId == roleId) {
                v.originContent.roleName = roleName
            }
        }
    }

    @Synchronized
    operator fun get(msgId: String): OriginAsrContentUnit? = contentCacheMap[msgId]

    @Synchronized
    internal fun findUntreated(): List<OriginAsrContentUnit> = contentCacheList.findAllList { it.isHandle == false }

    @Synchronized
    internal fun lastOrNull(): OriginAsrContentUnit? = contentCacheList.lastOrNull()

    private fun reverseFindAndReplace(oldResult: OriginAsrContentUnit, newResult: OriginAsrContentUnit) {
        for (i in contentCacheList.lastIndex downTo 0) {
            val find = contentCacheList[i]
            if (find === oldResult) {
                contentCacheList[i] = newResult
                break
            }
        }
    }

    data class OriginAsrContentUnit(
        val msgId: String,
        val originContent: ConvertContentItem,
        var isHandle: Boolean = false
    )
}