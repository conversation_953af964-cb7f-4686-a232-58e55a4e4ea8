/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - SubtitleLog.kt
 * Description:
 *     The log util in subtitles
 *
 * Version: 1.0
 * Date: 2025-07-25
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2025-07-25   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.translate.subtitle

import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.translate.BuildConfig

internal object SubtitleLog {

    /**
     * 字幕处理流程中输出冗余日志的专用Log工具，仅供特定情况下调试使用。
     * 默认状态下严禁启用，会频繁输出很多日志，影响其他问题分析，甚至触发日志限流导致线上问题日志丢失。
     */
    private const val ENABLE_VERBOSE_LOG = true

    /**
     * 启用严格模式，部分参数检查异常将被抛出
     */
    private val STRICT_MODE = BuildConfig.DEBUG

    @JvmStatic
    fun v(tag: String, msg: () -> String) {
        if (ENABLE_VERBOSE_LOG) {
            DebugUtil.i(tag, msg())
        }
    }

    @JvmStatic
    fun throws(tag: String, e: Throwable) {
        if (STRICT_MODE) {
            throw e
        }
        DebugUtil.e(tag, "throws: $e")
    }
}