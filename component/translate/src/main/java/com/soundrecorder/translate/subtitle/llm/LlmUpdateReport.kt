/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** File        : LlmUpdateReport.kt
 ** Description : LlmUpdateReport.kt
 ** Version     : 1.0
 ** Date        : 2025/08/01
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2025/08/01      1.0      create
 ***********************************************************************/
package com.soundrecorder.translate.subtitle.llm

data class LlmUpdateReport(
    val llmUnitKey: String,
    val requestId: String,
    val requestTime: Long,
    val responseTime: Long,
    val role: String,
    val originStr: String,
    val orgReferences: List<String>?,
    val llmReferences: List<String>?,
    val language: String,
    val resultStr: String?,
    val errCode: Int?,
    val errMsg: String?,
    val errExpends: String?,
) {
    fun toReportData(): Map<String, Any?> = mapOf(
        "llmUnitKey" to llmUnitKey,
        "requestId" to requestId,
        "requestTime" to requestTime,
        "responseTime" to responseTime,
        "role" to role,
        "originStr" to originStr,
        "orgReferences" to orgReferences,
        "llmReferences" to llmReferences,
        "language" to language,
        "resultStr" to resultStr,
        "errCode" to errCode,
        "errMsg" to errMsg,
        "errExpends" to errExpends,
    )
}