/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** File        : AsrContentManager.kt
 ** Description : AsrContentManager.kt
 ** Version     : 1.0
 ** Date        : 2025/08/01
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2025/08/01      1.0      create
 ***********************************************************************/

package com.soundrecorder.translate.subtitle

import android.content.Context
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.LanguageUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.realtimeasr.IRealtimeSubtitleCache
import com.soundrecorder.common.realtimeasr.OnRealtimeListener
import com.soundrecorder.translate.subtitle.cache.AsrFinalContentCache
import com.soundrecorder.translate.subtitle.cache.AsrOriginContentCache
import com.soundrecorder.translate.util.SubtitleDebugExportHelper
import kotlinx.coroutines.launch
import java.util.Locale

class AsrContentManager(
    context: Context,
    private val subtitleListenerGetter: () -> OnRealtimeListener?
) : AsrResultFilter.IOnAsrResultFiltered, LlmUpdateManager.IOnLlmUpdateResult, AsrRestructureHelper.OnSegmentProcessedListener {

    companion object {
        private const val TAG = "AsrContentManager"
    }

    private val asrLanguageConf = mutableMapOf<String, String>()
    private var latestAsrLanguage = Locale.CHINESE.language

    private val originAsrFilter = AsrResultFilter(LanguageUtil.DEFAULT_EXPECTED_WORDS, this)
    private val restructureHelper = AsrRestructureHelper(LanguageUtil.DEFAULT_EXPECTED_WORDS, originAsrFilter.assertLossCharSize, this)
    private val llmUpdateMgr = LlmUpdateManager.create(context, restructureHelper.llmMaxHistorySize, this, originAsrFilter.filterScope)

    /**
     * 一级缓存,过滤后的原始ASR内容
     */
    private val originAsrCache = AsrOriginContentCache()

    /**
     * 最终上屏及存储的实时字幕内容，并进行最终的讲话人名称修正
     */
    private val finalAsrCache = AsrFinalContentCache()

    private fun getAsrLanguage(channelId: String?): String = channelId?.let { asrLanguageConf[it] } ?: latestAsrLanguage

    fun onAsrResult(channelId: String?, msgId: String, result: ConvertContentItem) {
        SubtitleLog.v(TAG) { "onAsrResult: channelId=$channelId, msgId=$msgId, result=$result" }
        result.asrLanguage = getAsrLanguage(channelId)
        originAsrFilter.onAsrResult(channelId, msgId, result)
    }

    /**
     * 设置ASR识别语种，必须设置且匹配AiAsrKit的配置
     */
    fun configureAsrLanguage(channelId: String?, languageCode: String) {
        latestAsrLanguage = languageCode
        channelId?.let { asrLanguageConf[it] = languageCode }
        val outputParagraphSize = LanguageUtil.getExpectedWords(languageCode)
        DebugUtil.d(TAG, "configureAsrLanguage: channelId=$channelId, language=$languageCode, paragraphSize=$outputParagraphSize")
        originAsrFilter.configureParagraphSize(outputParagraphSize)
        restructureHelper.configureParagraphSize(outputParagraphSize, originAsrFilter.assertLossCharSize)
        llmUpdateMgr.asrLanguageCode = languageCode
    }

    fun updateRoleName(roleId: Int, roleName: String) {
        originAsrFilter.filterScope.launch {
            originAsrCache.updateRoleName(roleId, roleName)
            finalAsrCache.updateRoleName(roleId, roleName)
            notifySubtitleListener()
        }
    }

    private fun notifySubtitleListener() {
        subtitleListenerGetter()?.onSubtitleUpdated(finalAsrCache)
    }

    fun release() {
        llmUpdateMgr.release()
        originAsrFilter.release()
    }

    fun getRealtimeSubtitleCache(): IRealtimeSubtitleCache = finalAsrCache


    @Synchronized
    override fun onAsrResultFiltered(channelId: String?, msgId: String, content: ConvertContentItem) {
        SubtitleLog.v(TAG) { "onAsrResultFiltered: channelId=$channelId, msgId=$msgId, content=$content" }
        SubtitleDebugExportHelper.export()?.writeFilterAsr(channelId, msgId, content)
        originAsrCache[msgId] = AsrOriginContentCache.OriginAsrContentUnit(msgId, content)
        processSegment()
        processTextOptimization()
    }

    @Synchronized
    override fun onLlmUpdateResult(llmUnitKey: String, resultContent: String?) {
        SubtitleLog.v(TAG) { "onLlmUpdateResult: llmUnitKey=$llmUnitKey, resultContent=$resultContent" }
        finalAsrCache.findItemById(llmUnitKey)?.let { contentItem ->
            contentItem.textOptimizationState = ConvertContentItem.TextOptimizationState.COMPLETED
            resultContent?.let { contentItem.textContent = it }
        }
        notifySubtitleListener()
    }

    /**
     * 处理拆分整合
     */
    @Synchronized
    private fun processSegment() {
        restructureHelper.processSegment(originAsrCache, finalAsrCache)
        finalAsrCache.lastOrNullItem()?.let { SubtitleDebugExportHelper.export()?.writeMergedAsr(it) }
        notifySubtitleListener()
    }

    @Synchronized
    override fun onMergeTimeout(contentItem: ConvertContentItem) {
        SubtitleDebugExportHelper.export()?.writeMergedAsr(contentItem)
        contentItem.textOptimizationState = ConvertContentItem.TextOptimizationState.PROCESSING
        llmUpdateMgr.startRequest(contentItem.id, contentItem.roleId, contentItem.textContent)
    }

    /**
     * 处理大模型文本优化
     */
    @Synchronized
    private fun processTextOptimization() {
        finalAsrCache.findNeedOptimizedItems().forEach {
            it.textOptimizationState = ConvertContentItem.TextOptimizationState.PROCESSING
            llmUpdateMgr.startRequest(it.id, it.roleId, it.textContent)
        }
    }
}