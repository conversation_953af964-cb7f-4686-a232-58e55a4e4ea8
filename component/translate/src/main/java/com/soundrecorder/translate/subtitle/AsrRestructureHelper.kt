/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** File        : AsrRestructureHelper.kt
 ** Description : AsrRestructureHelper.kt
 ** Version     : 1.0
 ** Date        : 2025/08/01
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2025/08/01      1.0      create
 ***********************************************************************/
package com.soundrecorder.translate.subtitle

import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.translate.asr.realtime.RealTimeAsrParser.Companion.ASR_RESULT_TYPE_INTERMEDIATE
import com.soundrecorder.translate.asr.realtime.RealTimeAsrParser.Companion.ASR_RESULT_TYPE_VAD_FINAL
import com.soundrecorder.translate.subtitle.cache.AsrFinalContentCache
import com.soundrecorder.translate.subtitle.cache.AsrOriginContentCache
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlin.math.min
import kotlin.math.roundToInt

/**
 * 1. VAD_FINAL时，总字数为预期段落字数的150%，则需要拆段，且要保证每段至少有预期字数的50%，否则不拆
 * 2. VAD_FINAL时，字数不超过预期段落字数的10%，则认定为是零碎语句。
 *     2.1 零碎语句出现后，与下一个新的INTERMEDIATE句子出现时间间隔不超过2000ms，则合并为一个大的INTERMEDIATE继续处理
 *     2.2 合并处理中的段落出现VAD_FINAL的后，则需判断字数是否超过预期段落字数的10%，如果超过则不再继续合并，将整体作为一个VAD_FINAL继续触发大模型优化和单独分段上屏，如果未超过则继续根据2.1的规则合并
 *     2.3 当连续合并三个VAD_FINAL的零碎语句后，直接作为一个整体VAD_FINAL触发大模型优化和单独分段上屏
 *     2.4 若合并的零碎语句在VAD_FINAL的时候，发现讲话人不同，则立即停止继续合并，并触发拆段，将不同的讲话人拆开，无论字数多少
 *     2.5 若零碎语句VAD_FINAL后，与下一个新INTERMEDIATE出现的时间间隔超过2000ms，则不合并，直接将已合并部分（也可能从未发生合并，就一句）认定为真正的VAD_FINAL，进行大模型处理和上屏。 新INTERMEDIATE则继续根据上述规则处理。
 */
class AsrRestructureHelper(
    private var outputParagraphSize: Int,
    private var filterLossCharSize: Int,
    private val segmentProcessListener: OnSegmentProcessedListener
) {
    companion object {
        private const val TAG = "AsrContentRestructureHelper"
        private const val EXPECTED_LLM_PARAGRAPH_SIZE_RATE = 0.5f
        private const val LLM_MAX_HISTORY_RATE = 10f
        private const val SHORT_SENTENCE_THRESHOLD = 0.1f
        private const val LONG_PARAGRAPH_THRESHOLD = 1.5f
        private const val SPLIT_MIN_SEGMENT_THRESHOLD = 0.5f
        private const val INTERMEDIATE_MERGE_THRESHOLD_MS = 2000L
        private const val VAD_FINAL_MAX_INTERVAL_MS = 1000L
        private const val INTERMEDIATE_TO_FINAL_MAX_INTERVAL_MS = 800L
        private const val MAX_SHORT_SENTENCE_MERGE_COUNT = 3
        private const val LLM_ENDING_ELLIPSIS = "…"

        private val punctuationRegex = Regex("""[.、，。：；！？,:;!?]""")
        private val halfAngleRegex = Regex("""[.,:;!?]""")

        /**
         * 大模型返回的优化后内容：
         * 1.可能会在两侧有多余的空白字符，使用[trim]去除；
         * 2.结尾可能出现“……”，需要去除并替换为原始的结尾标点；
         * 3.结尾缺失标点符号，需要补充原始的结尾标点；
         */
        @JvmStatic
        fun fixLlmResultPunctuation(originStr: String, llmStr: String): String {
            var llmContent = llmStr.trim()
            while (llmContent.endsWith(LLM_ENDING_ELLIPSIS)) {
                val removeStart = llmContent.length - LLM_ENDING_ELLIPSIS.length
                llmContent = llmContent.removeRange(removeStart, llmContent.length)
            }
            if (punctuationRegex.matches("${llmContent.last()}")) {
                return llmContent
            }
            val originLast = "${originStr.last()}"
            if (punctuationRegex.matches(originLast)) {
                llmContent += originLast
            }
            return llmContent
        }
    }

    val llmMaxHistorySize: Int = (LLM_MAX_HISTORY_RATE / EXPECTED_LLM_PARAGRAPH_SIZE_RATE).roundToInt()

    private var shortSentenceSize = -1 // Must init with [configureParagraphSize]
    private var longParagraphSize = -1 // Must init with [configureParagraphSize]
    private var splitMinSegmentSize = -1 // Must init with [configureParagraphSize]

    private var shortSentenceMergeJob: Job? = null
    private val coroutineScope = CoroutineScope(Dispatchers.Default + SupervisorJob())

    init {
        configureParagraphSize(outputParagraphSize, filterLossCharSize)
        if (splitMinSegmentSize < 0) {
            throw IllegalArgumentException("$TAG: splitMinSegmentSize < 0")
        }
        if (longParagraphSize < 0) {
            throw IllegalArgumentException("$TAG: longParagraphSize < 0")
        }
        if (shortSentenceSize < 0) {
            throw IllegalArgumentException("$TAG: shortSentenceSize < 0")
        }
    }

    fun configureParagraphSize(outputParagraphSize: Int, filterLossCharSize: Int) {
        this.outputParagraphSize = outputParagraphSize
        this.filterLossCharSize = filterLossCharSize
        longParagraphSize = (outputParagraphSize * LONG_PARAGRAPH_THRESHOLD).toInt()
        splitMinSegmentSize = (outputParagraphSize * SPLIT_MIN_SEGMENT_THRESHOLD).toInt()
        shortSentenceSize = (outputParagraphSize * SHORT_SENTENCE_THRESHOLD).toInt()
    }

    private fun splitTimeOffset(originText: String, splitContents: List<String>, startOffset: Long, endOffSet: Long): List<Pair<Long, Long>> {
        if (splitContents.size <= 1) {
            return listOf(startOffset to endOffSet)
        }
        val originNum = originText.length
        val originDuration = endOffSet - startOffset
        val results = mutableListOf<Pair<Long, Long>>()
        var start = startOffset
        splitContents.forEachIndexed { index, content ->
            val splitNum = content.length
            val splitDuration = (splitNum.toFloat() / originNum.toFloat()) * originDuration
            val end = if (index == splitContents.lastIndex) {
                endOffSet
            } else {
                start + splitDuration.toLong()
            }
            results.add(start to end)
            start = end
        }
        return results
    }

    private fun splitTextContent(originText: String, paragraphSize: Int, minParagraphSize: Int): List<String> {
        val punctuationIndexes = punctuationRegex.findAll(originText).map { it.range.first }.toMutableSet()
        punctuationIndexes.add(originText.lastIndex)
        val splitIndexes = mutableListOf<Int>()
        var splitChars = paragraphSize + 1 // index从0开始，计算字数时需+1以包含index位置本身
        var lastFindIndex = 0
        var lastSplitIndex = 0
        for (i in punctuationIndexes) {
            if (lastFindIndex == 0) {
                lastFindIndex = i
                continue
            }
            val isFindSplit = run {
                if (i < splitChars || lastFindIndex > splitChars) return@run false
                // 分割长度至少满足最小分段长度
                return@run lastFindIndex + 1 - lastSplitIndex >= minParagraphSize
            }
            // 很长一段都没标点断句的情况
            val isExceedSplit = lastFindIndex > splitChars && lastFindIndex < originText.length
            if (isFindSplit || isExceedSplit) {
                splitIndexes.add(lastFindIndex)
                lastSplitIndex = lastFindIndex
                splitChars = lastFindIndex + paragraphSize + 1
                if (splitChars >= originText.length) {
                    break // 剩余部分已包含末端不稳定字符，不再继续分割
                }
            }
            lastFindIndex = i
        }
        if (splitIndexes.isEmpty()) {
            return listOf(originText)
        }
        splitIndexes.add(originText.lastIndex)
        val results = mutableListOf<String>()
        var splitLastIndex = 0
        splitIndexes.forEach {
            val start = splitLastIndex
            val end = min(it + 1, originText.length) // 标点本身分割至上一段的末尾，而非下一段的开头
            val splitStr = originText.substring(start, end)
            results.add(splitStr)
            splitLastIndex = end
        }
        return results
    }

    private fun splitAsrContent(asrContent: ConvertContentItem, paragraphSize: Int, minParagraphSize: Int): Map<Int, ConvertContentItem> {
        val originText = asrContent.textContent
        if (originText.length < paragraphSize) {
            return mapOf(0 to asrContent)
        }
        val splitContents = splitTextContent(originText, paragraphSize, minParagraphSize)
        if (splitContents.size <= 1) {
            return mapOf(0 to asrContent)
        }
        val splitOffsets = splitTimeOffset(originText, splitContents, asrContent.startTime, asrContent.endTime)
        if (splitContents.size != splitOffsets.size) {
            throw IllegalStateException("$TAG: splitContents and splitOffsets size not match")
        }
        // 必须为[LinkedHashMap]以确保添加顺序，[mutableMapOf]默认为[LinkedHashMap]
        val results = mutableMapOf<Int, ConvertContentItem>()
        for (i in 0 until splitContents.size) {
            val text = splitContents[i]
            val (start, end) = splitOffsets[i]
            val splitAsr = asrContent.copy(
                startTime = start, endTime = end, textContent = text
            )
            results[i] = splitAsr
        }
        return results
    }

    /**
     * 英文标点等半角符号，合并时需确保标点后有空格以避免影响观感
     */
    private fun handleMergeText(mergedText: String, newText: String): String {
        if (mergedText.isEmpty()) {
            return newText
        }
        if (newText.isEmpty()) {
            return mergedText
        }
        if (!halfAngleRegex.matches("${mergedText.last()}")) {
            return mergedText + newText
        }
        if (newText.first().isWhitespace()) {
            return mergedText + newText
        }
        return "$mergedText $newText"
    }

    /**
     * @param sourceItem 目标数据
     * @param beforeMergeItem 合并前的数据，如果为null，则代表是更新数据，否则是合并数据
     */
    private fun ConvertContentItem.updateFrom(sourceItem: ConvertContentItem, beforeMergeItem: ConvertContentItem? = null) {
        if (beforeMergeItem == null) {
            this.startTime = sourceItem.startTime
            this.endTime = sourceItem.endTime
            this.textContent = sourceItem.textContent
            this.roleId = sourceItem.roleId
            this.roleName = sourceItem.roleName
            this.vadType = sourceItem.vadType
            this.asrLanguage = sourceItem.asrLanguage
            this.asrChannelId = sourceItem.asrChannelId
        } else {
            this.textContent = handleMergeText(beforeMergeItem.textContent, sourceItem.textContent)
            this.vadType = sourceItem.vadType
            this.endTime = sourceItem.endTime
        }
    }

    private fun putItemToFinalCache(contentCache: AsrFinalContentCache, sourceItem: ConvertContentItem) {
        contentCache.putItem(sourceItem)
    }

    /**
     * 更新最终缓存中内容
     */
    @Synchronized
    private fun updateFinalCache(finalAsrCache: AsrFinalContentCache, contentItem: ConvertContentItem) {
        val processingItem = finalAsrCache.lastOrNullItem()
        // 二级缓存中当前段落并未处理完毕
        if (processingItem?.vadType == ASR_RESULT_TYPE_INTERMEDIATE) {
            processingItem.updateFrom(contentItem)
        } else {
            // 二级缓存中需新起一段
            putItemToFinalCache(finalAsrCache, contentItem)
        }
    }

    /**
     * 标记为已处理状态
     */
    private fun markProcessed(untreatedItemList: List<AsrOriginContentCache.OriginAsrContentUnit>) {
        untreatedItemList.forEach { it.isHandle = true }
    }

    /**
     * 处理拆分场景
     */
    private fun handleParagraphSplitByLength(finalAsrCache: AsrFinalContentCache, workingContentItem: ConvertContentItem): Boolean {
        if (workingContentItem.textContent.length >= longParagraphSize) {
            val splitItemMap = splitAsrContent(workingContentItem, outputParagraphSize, splitMinSegmentSize)
            // 将拆分后的数据项添加到缓存中
            splitItemMap.values.forEach { updateFinalCache(finalAsrCache, it) }
            return true
        }
        return false
    }

    /**
     * 处理单段场景
     */
    private fun handleSingleParagraphByLength(finalAsrCache: AsrFinalContentCache, workingContentItem: ConvertContentItem): Boolean {
        if (workingContentItem.textContent.length > shortSentenceSize) {
            // 直接作为单独的一段
            updateFinalCache(finalAsrCache, workingContentItem)
            return true
        }
        return false
    }

    /**
     * 处理讲话人不一致的场景
     */
    private fun handleSpeakerChangeSplit(
        finalAsrCache: AsrFinalContentCache,
        workingContentItem: ConvertContentItem,
        beforeMergeItem: ConvertContentItem?,
        currentMergeItem: AsrOriginContentCache.OriginAsrContentUnit,
    ): Boolean {
        if (workingContentItem.roleId != currentMergeItem.originContent.roleId) {
            // 将tempContent恢复到合并前的数据，并更新到二级缓存
            beforeMergeItem?.let { workingContentItem.updateFrom(it) }
            updateFinalCache(finalAsrCache, workingContentItem)
            // 将item的数据也更新到二级缓存
            updateFinalCache(finalAsrCache, currentMergeItem.originContent)
            return true
        }
        return false
    }

    /**
     * 根据字数处理拆分场景和单句满足一段的场景
     */
    private fun handleContentSplitAndSingleByLength(finalAsrCache: AsrFinalContentCache, workingContentItem: ConvertContentItem): Boolean {
        if (handleParagraphSplitByLength(finalAsrCache, workingContentItem)) {
            return true
        }

        if (handleSingleParagraphByLength(finalAsrCache, workingContentItem)) {
            return true
        }

        return false
    }

    /**
     * 处理合并场景
     */
    private fun handelContentMerge(
        originAsrCache: AsrOriginContentCache,
        finalAsrCache: AsrFinalContentCache,
        workingContentItem: ConvertContentItem
    ) {
        updateFinalCache(finalAsrCache, workingContentItem.copy().apply { vadType = ASR_RESULT_TYPE_INTERMEDIATE })
        shortSentenceMergeJob = coroutineScope.launch {
            delay(INTERMEDIATE_MERGE_THRESHOLD_MS)
            if (!isActive) return@launch
            val lastItem = finalAsrCache.lastOrNullItem() ?: return@launch
            // 合并超时，将类型设置为最终态，表示处理完成。
            lastItem.vadType = ASR_RESULT_TYPE_VAD_FINAL
            segmentProcessListener.onMergeTimeout(lastItem)
            markProcessed(originAsrCache.findUntreated())
        }
    }

    fun processSegment(originAsrCache: AsrOriginContentCache, finalAsrCache: AsrFinalContentCache) {
        shortSentenceMergeJob?.cancel()

        if (finalAsrCache.lastOrNullItem() == null) {
            // 缓存中没有内容,则直接将一级缓存中的当前数据给到二级缓存，通常只有一个，且处于中间态
            val originCurrentContent = originAsrCache.lastOrNull() ?: return
            updateFinalCache(finalAsrCache, originCurrentContent.originContent.copy())
            return
        }

        val untreatedItemList = originAsrCache.findUntreated()
        var beforeMergeItem: ConvertContentItem? = null
        val workingContentItem = ConvertContentItem()

        for ((index, item) in untreatedItemList.withIndex()) {
            if (index == 0) {
                workingContentItem.updateFrom(item.originContent)

                if (workingContentItem.vadType == ASR_RESULT_TYPE_INTERMEDIATE) {
                    updateFinalCache(finalAsrCache, workingContentItem)
                    return
                }

                if (handleContentSplitAndSingleByLength(finalAsrCache, workingContentItem)) {
                    markProcessed(untreatedItemList)
                    return
                }

                beforeMergeItem = workingContentItem.copy()
                handelContentMerge(originAsrCache, finalAsrCache, workingContentItem)
                continue
            }

            // 处于合并状态，将上一次延时任务取消
            shortSentenceMergeJob?.cancel()

            // 触发零碎语句合并
            workingContentItem.updateFrom(item.originContent, beforeMergeItem)

            if (workingContentItem.vadType == ASR_RESULT_TYPE_INTERMEDIATE) {
                updateFinalCache(finalAsrCache, workingContentItem)
                return
            }

            // 判断讲话人是否一致
            if (handleSpeakerChangeSplit(finalAsrCache, workingContentItem, beforeMergeItem, item)) {
                markProcessed(untreatedItemList)
                return
            }

            if (handleContentSplitAndSingleByLength(finalAsrCache, workingContentItem)) {
                markProcessed(untreatedItemList)
                return
            }

            // 如果合并次数大于等于3次，则停止合并,直接更新数据，更新标记
            if (index > MAX_SHORT_SENTENCE_MERGE_COUNT - 1) {
                updateFinalCache(finalAsrCache, workingContentItem)
                markProcessed(untreatedItemList)
                return
            }

            beforeMergeItem = workingContentItem.copy()
            handelContentMerge(originAsrCache, finalAsrCache, workingContentItem)
        }
    }

    interface OnSegmentProcessedListener {
        fun onMergeTimeout(contentItem: ConvertContentItem)
    }
}