apply from: "../../common_build.gradle"

android {
    namespace 'com.soundrecorder.translate'

    buildFeatures {
        buildConfig true
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
    implementation project(path: ':common:libbase')
    implementation project(path: ':common:libcommon')
    implementation project(path: ':common:RecorderLogBase')
    implementation project(':common:modulerouter')

    implementation libs.androidx.core.ktx
    // 必须，AISDK的core、download等都会依赖toolkits
    implementation libs.oplus.aiunit.toolkit
    // 必须，跨进程下载sdk
    implementation libs.oplus.aiunit.download

//    // 如果使用AI子系统新版SDK
//    implementation "com.oplus.aiunit.open:aisubsystem:${ai_sdk_version}"
//    // 必须，核心的数据通信、服务鉴权、兼容校验库
    implementation libs.oplus.aiunit.core
    // Koin for Android
    implementation(libs.koin)

    implementation libs.oplus.aiunit.asr
    implementation libs.oplus.aiunit.textgenerate
    implementation libs.oplus.aikit.base
    implementation libs.gson
}