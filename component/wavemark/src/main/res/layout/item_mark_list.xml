<?xml version="1.0" encoding="utf-8"?>
<com.soundrecorder.common.widget.ClickScaleCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mark_item_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@null"
    android:forceDarkAllowed="false"
    android:elevation="0dp"
    app:exclude_view_tags="btnMenuMore,img_mark_photo">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/mark_item_height"
        android:clickable="true"
        android:gravity="center_vertical"
        android:longClickable="true"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_mark_icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_mark_small"
            android:contentDescription="@string/talkback_flag"/>

        <TextView
            android:id="@+id/text_mark_time"
            app:layout_constraintStart_toEndOf="@+id/iv_mark_icon"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp16"
            android:fontFamily="sys-sans-en"
            android:fontFeatureSettings="tnum"
            android:textFontWeight="500"
            android:textColor="@color/coui_color_primary_neutral"
            android:textSize="@dimen/dp16"
            tools:text="00:03" />

        <TextView
            android:id="@+id/text_mark_description"
            app:layout_constraintStart_toEndOf="@+id/text_mark_time"
            app:layout_constraintEnd_toStartOf="@+id/img_mark_photo"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp20"
            android:ellipsize="end"
            android:fontFamily="sans-serif-medium"
            android:gravity="center_vertical|start"
            android:maxLines="1"
            android:textAlignment="viewStart"
            android:textColor="@color/coui_color_primary_neutral"
            android:textSize="@dimen/dp16"
            tools:text="标记 2" />

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/img_mark_photo"
            app:layout_constraintEnd_toStartOf="@+id/btnMenuMore"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginEnd="@dimen/dp16"
            android:layout_width="@dimen/dp42"
            android:layout_height="@dimen/dp42"
            android:contentDescription="@string/talkback_preview_mark_picture"
            android:forceDarkAllowed="false"
            android:foreground="@null"
            android:padding="6dp"
            android:scaleType="centerCrop"
            android:tag="img_mark_photo"
            android:visibility="visible"
            app:strokeColor="@color/picture_mark_icon_bord_color"
            app:strokeWidth="0.5dp" />

        <ImageButton
            android:id="@+id/btnMenuMore"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="@dimen/dp24"
            android:layout_height="@dimen/dp48"
            android:background="@drawable/menu_more_pressed"
            android:contentDescription="@string/abc_action_menu_overflow_description"
            android:foreground="@null"
            android:scaleType="center"
            android:src="@drawable/icon_mark_more"
            android:tag="btnMenuMore" />
        <View
            android:id="@+id/divider_line"
            android:background="@color/coui_color_divider"
            android:layout_width="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="@+id/text_mark_time"
            app:layout_constraintEnd_toEndOf="@id/btnMenuMore"
            android:layout_height="@dimen/dp0_33"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.soundrecorder.common.widget.ClickScaleCardView>