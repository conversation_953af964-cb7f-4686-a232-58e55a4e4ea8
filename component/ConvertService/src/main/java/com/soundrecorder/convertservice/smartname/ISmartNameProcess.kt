/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  IBackgroundProcess
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.smartname

import com.soundrecorder.common.databean.SmartNameParam
import com.soundrecorder.modulerouter.smartname.ISmartNameCallback

interface ISmartNameProcess {

    fun startSmartName(mediaId: Long, isRealTime: Boolean, params: SmartNameParam?): Boolean

    fun cancelSmartName(mediaId: Long): Boolean
    fun releaseSmartName(mediaId: Long) {
    }
    fun registerSmartNameCallback(mediaId: Long, callback: ISmartNameCallback) {}
    fun unregisterSmartNameCallback(mediaId: Long, callback: ISmartNameCallback) {}
}