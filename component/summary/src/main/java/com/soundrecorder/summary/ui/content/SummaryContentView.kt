/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryContentView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content

import android.animation.Animator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.text.method.LinkMovementMethod
import android.text.style.URLSpan
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewTreeObserver
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.coui.appcompat.textview.COUITextView
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.coui.appcompat.tips.def.COUIDefaultTopTips
import com.oplus.anim.EffectiveAnimationView
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.px2dp
import com.soundrecorder.base.utils.LanguageUtil
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.base.utils.PrefUtil.SUMMARY_SUPPORT_THEME
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_ACTION_EXPORT
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_EXPORT_DOCUMENT
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_FILE_GENERATION_FAILED
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_FORMAT_PDF
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_FORMAT_WORD
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_RETURN_FAILURE
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_RETURN_SUCCESS
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_TYPE_SUMMARY
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.exportfile.ExportDoc
import com.soundrecorder.common.exportfile.ExportPdf
import com.soundrecorder.common.exportfile.SummaryContentViewUtil
import com.soundrecorder.common.exportfile.ui.ExportTipsManager
import com.soundrecorder.common.share.ShareSummaryCopy
import com.soundrecorder.common.share.ShareTextContent
import com.soundrecorder.common.utils.AITextStyleUtil
import com.soundrecorder.common.utils.gone
import com.soundrecorder.common.utils.visible
import com.soundrecorder.common.view.COUIScrollView
import com.soundrecorder.common.widget.OSImageView
import com.soundrecorder.modulerouter.share.ShareAction
import com.soundrecorder.modulerouter.summary.IAISummaryCallback
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.summary.data.SummaryDataParser
import com.soundrecorder.summary.model.SummaryAgentEvent
import com.soundrecorder.summary.model.SummaryEntity
import com.soundrecorder.summary.model.SummaryModel
import com.soundrecorder.summary.model.SummarySupportTheme
import com.soundrecorder.summary.model.SummaryTheme
import com.soundrecorder.summary.model.SummaryTrace
import com.soundrecorder.summary.request.AISummaryProcess.Companion.IS_OVER_SIZE
import com.soundrecorder.summary.request.AISummaryProcess.Companion.THEME_CODE
import com.soundrecorder.summary.ui.content.SummaryAnimateTextView.TextAnimationListener
import com.soundrecorder.summary.ui.content.SummaryExportToNote.ContentViewParams
import com.soundrecorder.summary.ui.content.SummaryExportToNote.ExportRequest
import com.soundrecorder.summary.ui.content.callback.ISummaryFunctionCallback
import com.soundrecorder.summary.ui.content.callback.SummaryActionModeCallback
import com.soundrecorder.summary.ui.content.span.AgentClickPlugin
import com.soundrecorder.summary.ui.content.span.ConfigPlugin
import com.soundrecorder.summary.ui.content.span.SummaryLinkResolver
import com.soundrecorder.summary.ui.content.summarytemplate.SceneBottomSheetDialogFragment
import com.soundrecorder.summary.ui.content.summarytemplate.SceneDialogFragment
import com.soundrecorder.summary.ui.content.summarytemplate.SceneItem
import io.noties.markwon.Markwon
import io.noties.markwon.SoftBreakAddsNewLinePlugin
import io.noties.markwon.ext.tasklist.TaskListPlugin
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference

class SummaryContentView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : COUIScrollView(context, attrs, defStyleAttr), OnClickListener, ViewTreeObserver.OnGlobalLayoutListener {

    companion object {
        private const val TAG = "SummaryContentView"
        private const val LOADING_TIPS_DELAY = 1000L

        private const val ITEM_POSITION_0 = 0
        private const val ITEM_POSITION_1 = 1

        private const val SIZE_TWO = 2

        private const val TAG_NEXT = "next"
        private const val TAG_REFRESH = "refresh"
        private const val DOC_SUFFIX = "doc"
        private const val PDF_SUFFIX = "pdf"

        private const val SCROLLER_CHECK_TIME = 50L

        private const val POSITION_SIMPLE = 0
        private const val POSITION_DETAIL = 1

        private const val SCROLL_TO_BOTTOM_DELAY = 300L

        private const val OPACITY = 255
        private const val DISABLE_ALPHA = 51
    }

    var currentScrollState: Int = Constants.SCROLL_STATE_IDLE

    //已经设置了数据，但是不一定动画结束了
    var isFinishUploadSummary = false

    private lateinit var container: SummaryContentContainer
    private lateinit var loadingView: EffectiveAnimationView
    private lateinit var content: SummaryAnimateTextView
    private lateinit var cardContainer: LinearLayout
    private lateinit var copyRight: COUITextView
    private lateinit var divider: View
    private lateinit var toolBar: RelativeLayout
    private lateinit var copy: AppCompatImageView
    private lateinit var export: AppCompatImageView
    private lateinit var scene: COUITextView
    private lateinit var tab: LinearLayout
    private lateinit var simpleTab: COUIButton
    private lateinit var detailTab: COUIButton
    private lateinit var previous: AppCompatImageView
    private lateinit var refresh: AppCompatImageView
    private lateinit var errorView: ConstraintLayout
    private lateinit var errorIconView: OSImageView
    private lateinit var errorMsgText: COUITextView
    private lateinit var retry: COUITextView
    private lateinit var loadTip: COUIDefaultTopTips

    private var summaryChildFragmentManager: FragmentManager? = null

    private val agentDrawable: Drawable? by lazy {
        ResourcesCompat.getDrawable(
            context.resources,
            com.soundrecorder.summary.R.drawable.ic_agent_un_check,
            context.theme
        )
    }
    private val agentUnCheckDrawable: Drawable? by lazy {
        ResourcesCompat.getDrawable(
            context.resources,
            com.soundrecorder.summary.R.drawable.ic_agent_check,
            context.theme
        )
    }

    private val sceneSelectCollapsed by lazy {
        ContextCompat.getDrawable(context, com.soundrecorder.summary.R.drawable.ic_scene_select_collapsed)
    }

    private val sceneSelectExpand by lazy {
        ContextCompat.getDrawable(context, com.soundrecorder.summary.R.drawable.ic_scene_select_expand)
    }

    private val contentMarginLoadTip: Int by lazy {
        context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp16)
    }

    private val toolbarDefaultHeight: Int by lazy {
        context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp56)
    }

    private val scrollToVisibleOffset by lazy {
        context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp65)
    }

    private val summaryEntityHelper: SummaryEntityHelper by lazy {
        SummaryEntityHelper(context)
    }

    private val enableColor by lazy {
        COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelPrimary)
    }

    private val disableColor by lazy {
        COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelTertiary)
    }

    private val selectEnableColor by lazy {
        COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelPrimary)
    }

    private val selectDisableColor by lazy {
        COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelTertiary)
    }

    private val unSelectEnableColor by lazy {
        COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelSecondary)
    }

    private val unSelectDisableColor by lazy {
        COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelQuaternary)
    }

    private val shareAction by lazy {
        Injector.injectFactory<ShareAction>()
    }

    private val scrollToBottomRunnable = Runnable {
        val childHeight = container.height
        DebugUtil.d(TAG, "scrollToBottomRunnable childHeight = $childHeight")
        smoothScrollTo(0, childHeight)
    }

    private var sceneBottomSheet: SceneBottomSheetDialogFragment? = null

    private val exportTipsManager: ExportTipsManager by lazy { ExportTipsManager() }

    private var exportPopList: COUIPopupListWindow? = null
    private var entityPopList: COUIPopupListWindow? = null

    private val activity = context as Activity
    private val lifecycle = (context as? AppCompatActivity)?.lifecycleScope
    private var loadingJob: Job? = null
    private var summaryFunctionCallback: ISummaryFunctionCallback? = null
    private var summaryContentText: String = ""        // 渲染后的文本内容
    private var summaryOriginText: String = ""         // 原始摘要文本

    // 录音文件信息
    private var currentMediaId: Long = 0L
    private var currentRecordTitle: String = ""
    private var currentRecordDuration: Long = 0

    //识别出的主题、标题、语言等
    private var identifiedTheme: SummaryTheme = SummaryTheme(SummaryTheme.NORMAL)
    private var identifiedValue: String = ""

    private var currentTheme: SummaryTheme? = null
    private var agent = mutableListOf<SummaryAgentEvent>()
    private var summaryEntity = mutableListOf<SummaryEntity>()
    private var currentPosition = -1

    //结束加载，包括动画
    private var isLoadingFinish = false
    private var bgAnimator: ValueAnimator? = null
    private var supportThemes = mutableListOf<SummarySupportTheme>()
    private var lastScrollY = 0
    private var lastDragScrollY = 0
    private var gestureDetector: GestureDetector? = null
    private var markwon: Markwon? = null
    private var summaryCallback: IAISummaryCallback? = null

    //判断是否在加载中
    private var isLoadingTips = false


    private fun initChild() {
        initContainer()
        initLoading()
        initContent()
        initTools()
        initCardContainer()
        initOther()
        initErrorView()
        initLoadTip()
        initGestureDetector()
    }

    private fun initContainer() {
        LayoutInflater.from(context)
            .inflate(com.soundrecorder.summary.R.layout.layout_summary_container_view, this, true)
        container = findViewById(com.soundrecorder.summary.R.id.container_view)
    }

    private fun initLoading() {
        loadingView = findViewById(com.soundrecorder.summary.R.id.summary_loading)
    }

    private fun showLoadingTips() {
        copyRight.setText(com.soundrecorder.base.R.string.summary_loading_tips)
        val lp = copyRight.layoutParams as? ConstraintLayout.LayoutParams
        lp?.topToBottom = com.soundrecorder.summary.R.id.summary_loading
        lp?.topMargin = context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp12)
        copyRight.visible()
        isLoadingTips = true
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initContent() {
        content = findViewById(com.soundrecorder.summary.R.id.summary_content)
        content.highlightColor = Color.TRANSPARENT
        val actionModeCallback = SummaryActionModeCallback(content, true)
        content.customSelectionActionModeCallback = actionModeCallback
        content.setOnLongClickListener {
            content.highlightColor = actionModeCallback.getColor()
            false
        }
    }

    private fun initTools() {
        toolBar = findViewById(com.soundrecorder.summary.R.id.summary_tool_bar)
        copy = findViewById(com.soundrecorder.summary.R.id.copy)
        export = findViewById(com.soundrecorder.summary.R.id.export)
        scene = findViewById(com.soundrecorder.summary.R.id.summary_scene)
        previous = findViewById(com.soundrecorder.summary.R.id.previous)
        previous.gone()
        refresh = findViewById(com.soundrecorder.summary.R.id.refresh)
        tab = findViewById(com.soundrecorder.summary.R.id.tab_layout)
        simpleTab = findViewById(com.soundrecorder.summary.R.id.btn_simple_summary)
        detailTab = findViewById(com.soundrecorder.summary.R.id.btn_detail_summary)

        copy.setOnClickListener(this)
        export.setOnClickListener(this)
        scene.setOnClickListener(this)
        previous.setOnClickListener(this)
        refresh.setOnClickListener(this)
        simpleTab.setOnClickListener(this)
        detailTab.setOnClickListener(this)
    }

    private fun handleTabChange(newPos: Int) {
        if (currentPosition == newPos) return
        if (isLoadingFinish.not()) return
        when (newPos) {
            POSITION_DETAIL -> {
                switchDetailOrSimple(SummaryTheme.NORMAL)
                simpleTab.isSelected = false
                detailTab.isSelected = true
            }

            POSITION_SIMPLE -> {
                switchDetailOrSimple(SummaryTheme.NORMAL_SIMPLE)
                simpleTab.isSelected = true
                detailTab.isSelected = false
            }
        }
        currentPosition = newPos
    }

    private fun switchDetailOrSimple(id: Int) {
        if (currentTheme?.style != SummaryTheme.NORMAL
            && currentTheme?.style != SummaryTheme.NORMAL_SIMPLE) {
            return
        }
        val themeChange = id != currentTheme?.style
        DebugUtil.d(TAG, "switchDetailOrSimple themeChange = $themeChange, id = $id")
        if (themeChange) {
            val newTheme = SummaryTheme(id)
            summaryFunctionCallback?.onClickScene(newTheme)
        }
    }

    private fun updateSceneIcons(enable: Boolean) {
        scene.visible()
        scene.text = getThemeTitle(currentTheme)
        scene.isEnabled = enable
        val color = if (enable) { enableColor } else { disableColor }
        scene.setTextColor(color)
        val themeDrawable = ContextCompat.getDrawable(
            context,
            SummaryTheme.getDrawableRes(currentTheme?.style ?: SummaryTheme.NORMAL)
        )
        val expandIcon = if (isSceneBottomSheetShowing()) { sceneSelectCollapsed } else { sceneSelectExpand }
        scene.setCompoundDrawablesRelativeWithIntrinsicBounds(themeDrawable, null, expandIcon, null)
        val alpha = if (enable) { OPACITY } else { DISABLE_ALPHA }
        scene.compoundDrawablesRelative.forEach { it?.alpha = alpha }
    }

    private fun updateSceneTab(theme: Int, disableClick: Boolean = false) {
        if (theme == SummaryTheme.NORMAL || theme == SummaryTheme.NORMAL_SIMPLE) {
            tab.visible()
        } else {
            tab.gone()
        }

        val newPosition =
            if (theme == SummaryTheme.NORMAL_SIMPLE) POSITION_SIMPLE else POSITION_DETAIL
        if (currentPosition != newPosition) {
            currentPosition = newPosition
            simpleTab.isSelected = newPosition == POSITION_SIMPLE
            detailTab.isSelected = newPosition == POSITION_DETAIL
        }

        // 更新按钮状态
        simpleTab.isEnabled = disableClick.not()
        detailTab.isEnabled = disableClick.not()

        val selectColor = if (disableClick.not()) selectEnableColor else selectDisableColor
        val unSelectColor = if (disableClick.not()) unSelectEnableColor else unSelectDisableColor

        simpleTab.setTextColor(if (newPosition == POSITION_SIMPLE) selectColor else unSelectColor)
        detailTab.setTextColor(if (newPosition == POSITION_DETAIL) selectColor else unSelectColor)
    }

    private fun isSceneBottomSheetShowing(): Boolean {
        return sceneBottomSheet?.dialog?.isShowing == true
    }

    private fun initCardContainer() {
        cardContainer = findViewById(com.soundrecorder.summary.R.id.card_container)
    }

    private fun initOther() {
        copyRight = findViewById(com.soundrecorder.summary.R.id.copyright)
        divider = findViewById(com.soundrecorder.summary.R.id.divider_line)
    }

    private fun initErrorView() {
        errorView = findViewById(com.soundrecorder.summary.R.id.layout_error)
        errorIconView = findViewById(com.soundrecorder.summary.R.id.error_logo)
        errorMsgText = findViewById(com.soundrecorder.summary.R.id.error_msg_text)
        retry = findViewById(com.soundrecorder.summary.R.id.retry)
        retry.setOnClickListener(this)
        COUITextViewCompatUtil.setPressRippleDrawable(retry)
    }

    private fun initLoadTip() {
        loadTip = findViewById(com.soundrecorder.summary.R.id.load_tips)
        loadTip.setCloseDrawable(ContextCompat.getDrawable(context, com.support.tips.R.drawable.coui_ic_toptips_close))
        loadTip.setStartIcon(ContextCompat.getDrawable(context, com.soundrecorder.summary.R.drawable.ic_detail_floating_layer))
        loadTip.setAnimatorDismissListener(object : Animator.AnimatorListener {
            override fun onAnimationEnd(animation: Animator) {
                loadTipsGone()
            }

            override fun onAnimationStart(animation: Animator) {
            }

            override fun onAnimationCancel(animation: Animator) {
            }

            override fun onAnimationRepeat(animation: Animator) {
            }
        })
        loadTip.setCloseBtnListener {
            loadTip.dismissWithAnim()
        }
    }

    private fun initGestureDetector() {
        gestureDetector = GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
            override fun onScroll(
                e1: MotionEvent?,
                e2: MotionEvent,
                distanceX: Float,
                distanceY: Float
            ): Boolean {
                // 当用户手指拖动时触发
                setScrollState(Constants.SCROLL_STATE_DRAGGING)
                if (distanceY < 0 && canScrollVertically(-1).not()) {
                    summaryFunctionCallback?.onScrolled(distanceX.toInt(), distanceY.toInt())
                }
                return super.onScroll(e1, e2, distanceX, distanceY)
            }
        })
    }

    init {
        initChild()
    }

    override fun onDetachedFromWindow() {
        DebugUtil.d(TAG, "onDetachedFromWindow")
        super.onDetachedFromWindow()
        onStopLoading()
        content.cancelAnimation()
        exportPopList?.dismiss()
        entityPopList?.dismiss()
        bgAnimator?.cancel()
        exportTipsManager.release()
        gestureDetector = null
        errorIconView.release()
        lastScrollY = 0
        lastDragScrollY = 0
        removeCallbacks(scrollToBottomRunnable)
    }

    override fun onClick(v: View?) {
        v ?: return
        DebugUtil.d(TAG, "isLoadingFinish = $isLoadingFinish")
        if (isLoadingFinish.not()) {
            return
        }
        if (ClickUtils.isQuickClick()) {
            DebugUtil.d(TAG, "isQuickClick")
            return
        }
        when (v) {
            copy -> copySummary()
            export -> showExportPopMenu()
            scene -> showScenePopMenu()
            previous -> clickPrevious()
            refresh -> clickRefreshOrNext()
            retry -> clickRetry()
            simpleTab -> handleTabChange(POSITION_SIMPLE)
            detailTab -> handleTabChange(POSITION_DETAIL)
            else -> DebugUtil.w(TAG, "click what ? v $v")
        }
    }

    private fun copySummary() {
        summaryFunctionCallback?.onClickCopy()
        lifecycle ?: return
        lifecycle.launch {
            val title = getSummaryTitle()
            val time = withContext(Dispatchers.IO) {
                summaryCallback?.getSummaryTime() ?: ""
            }
            val shareTextContent = "$title\n$time\n${content.text}"
            if (shareTextContent.isEmpty()) {
                DebugUtil.w(TAG, "why is empty? Check again carefully")
                return@launch
            }
            shareAction?.share(
                activity,
                ShareTextContent(-1, false, "", 0, emptyList()),
                ShareSummaryCopy(shareTextContent),
                lifecycle,
                null
            )
        }
    }

    private fun showExportPopMenu() {
        exportPopList?.dismiss()
        lifecycle?.launch {
            val isSupportExportDoc = withContext(Dispatchers.Default) {
                ExportDoc.isSupportExport(context)
            }
            val exportItemList = mutableListOf<PopupListItem>().apply {
                val builder = PopupListItem.Builder()
                if (isSupportExportDoc) {
                    builder.setIconId(com.soundrecorder.summary.R.drawable.ic_export_doc)
                        .setTitle(
                            context.getString(
                                com.soundrecorder.common.R.string.summary_export_to,
                                context.getString(com.soundrecorder.common.R.string.word)
                            )
                        )
                        .setGroupId(com.soundrecorder.common.R.id.group1)
                    add(builder.build())
                }
                builder.reset()

                builder.setIconId(com.soundrecorder.summary.R.drawable.ic_export_pdf)
                    .setTitle(
                        context.getString(
                            com.soundrecorder.common.R.string.summary_export_to,
                            context.getString(com.soundrecorder.common.R.string.pdf)
                        )
                    )
                    .setGroupId(com.soundrecorder.common.R.id.group2)
                add(builder.build())


                builder.setIconId(com.soundrecorder.summary.R.drawable.ic_export_note)
                    .setTitle(context.getString(com.soundrecorder.common.R.string.summary_export_to_note))
                    .setGroupId(com.soundrecorder.common.R.id.group3)
                add(builder.build())
            }

            exportPopList = COUIPopupListWindow(context).apply {
                this.itemList = exportItemList
                this.anchorView = export
                this.resetOffset()
                this.setOnItemClickListener { _, _, pos, _ ->
                    if (ClickUtils.isQuickClick()) {
                        return@setOnItemClickListener
                    }
                    when {
                        itemList.size > SIZE_TWO -> {
                            when (pos) {
                                ITEM_POSITION_0 -> exportToDoc()
                                ITEM_POSITION_1 -> exportToPdf()
                                else -> exportToNote()
                            }
                        }

                        else -> {
                            when (pos) {
                                ITEM_POSITION_0 -> exportToPdf()
                                else -> exportToNote()
                            }
                        }
                    }
                    this.dismiss()
                }
                this.show()
            }
        }
    }

    private fun exportToDoc() {
        summaryFunctionCallback?.onClickExport(ISummaryFunctionCallback.ExportType.TO_WORD)
        lifecycle?.launch(Dispatchers.IO) {
            runCatching {
                val title = <EMAIL>()
                // 准备导出数据
                val exportData = SummaryContentViewUtil.prepareExportData(context, title, getOriginSummaryText())
                // 生成文件路径
                val targetPath = SummaryContentViewUtil.generateExportFilePath(context, DOC_SUFFIX, title)
                if (targetPath.isEmpty()) {
                    exportTipsManager.showExportError(context.getString(com.soundrecorder.common.R.string.save_failed))
                    return@launch
                }

                // 执行导出
                val success = ExportDoc.saveToWord(context, targetPath, exportData)

                withContext(Dispatchers.Main) {
                    //导出成功 弹窗 button 点击跳转文档预览文件
                    if (success) {
                        exportTipsManager.showExportProcessDialog(context, com.soundrecorder.common.R.string.summary_export_loading) {
                            exportTipsManager.showSnackBar(
                                context,
                                this@SummaryContentView,
                                context.getString(com.soundrecorder.common.R.string.summary_export_note_finish),
                                context.getString(com.soundrecorder.common.R.string.export_view_look)
                            ) {
                                SummaryContentViewUtil.openDocumentFile(context, targetPath)
                            }
                            /*添加埋点事件*/
                            AISummaryBuryingUtil.addRecordShareEvent(
                                AISummaryBuryingUtil.ShareEventInfo(
                                    currentMediaId.toString(), VALUE_TYPE_SUMMARY, VALUE_ACTION_EXPORT,
                                    VALUE_EXPORT_DOCUMENT, VALUE_FORMAT_WORD, VALUE_RETURN_SUCCESS, ""
                                )
                            )
                        }
                    } else {
                        exportTipsManager.showExportError(context.getString(com.soundrecorder.common.R.string.save_failed))
                        AISummaryBuryingUtil.addRecordShareEvent(
                            AISummaryBuryingUtil.ShareEventInfo(
                                currentMediaId.toString(), VALUE_TYPE_SUMMARY, VALUE_ACTION_EXPORT,
                                VALUE_EXPORT_DOCUMENT, VALUE_FORMAT_WORD, VALUE_RETURN_FAILURE, VALUE_FILE_GENERATION_FAILED
                            )
                        )
                    }
                }
            }.onFailure {
                DebugUtil.e(TAG, "exportFile error: ${it.message}")
            }
        }
    }

    private fun exportToPdf() {
        summaryFunctionCallback?.onClickExport(ISummaryFunctionCallback.ExportType.TO_PDF)
        lifecycle?.launch(Dispatchers.IO) {
            runCatching {
                val title = <EMAIL>()
                // 准备导出数据
                val exportData = SummaryContentViewUtil.prepareExportData(context, title, getOriginSummaryText())
                // 生成文件路径
                val targetPath = SummaryContentViewUtil.generateExportFilePath(context, PDF_SUFFIX, title)

                if (targetPath.isEmpty()) {
                    exportTipsManager.showExportError(context.getString(com.soundrecorder.common.R.string.save_failed))
                    return@launch
                }

                // 执行导出
                val success = ExportPdf.saveToPdf(context, targetPath, exportData)

                withContext(Dispatchers.Main) {
                    if (success) {
                        exportTipsManager.showExportProcessDialog(context, com.soundrecorder.common.R.string.summary_export_loading) {
                            exportTipsManager.showSnackBar(
                                context,
                                this@SummaryContentView,
                                context.getString(com.soundrecorder.common.R.string.summary_export_note_finish),
                                context.getString(com.soundrecorder.common.R.string.export_view_look)
                            ) {
                                SummaryContentViewUtil.openDocumentFile(context, targetPath)
                            }
                            /*添加埋点事件*/
                            AISummaryBuryingUtil.addRecordShareEvent(
                                AISummaryBuryingUtil.ShareEventInfo(
                                    currentMediaId.toString(), VALUE_TYPE_SUMMARY, VALUE_ACTION_EXPORT,
                                    VALUE_EXPORT_DOCUMENT, VALUE_FORMAT_PDF, VALUE_RETURN_SUCCESS, ""
                                )
                            )
                        }
                    } else {
                        exportTipsManager.showExportError(context.getString(com.soundrecorder.common.R.string.save_failed))

                        AISummaryBuryingUtil.addRecordShareEvent(
                            AISummaryBuryingUtil.ShareEventInfo(
                                currentMediaId.toString(), VALUE_TYPE_SUMMARY, VALUE_ACTION_EXPORT,
                                VALUE_EXPORT_DOCUMENT, VALUE_FORMAT_PDF, VALUE_RETURN_FAILURE, VALUE_FILE_GENERATION_FAILED
                            )
                        )
                    }
                }
            }.onFailure {
                DebugUtil.e(TAG, "exportToPdf error: ${it.message}")
            }
        }
    }

    private fun exportToNote() {
        summaryFunctionCallback?.onClickExport(ISummaryFunctionCallback.ExportType.TO_NOTE)
        val exportRequest = ExportRequest(context, currentMediaId, getSummaryTitle(), getRecordFilePath())
        val contentViewParams = ContentViewParams(
            lifecycle ?: return,
            exportTipsManager,
            WeakReference(this@SummaryContentView)
        )
        SummaryExportToNote.exportSummaryToNoteFromContentView(exportRequest, contentViewParams)
    }

    fun setChildFragmentManager(fm: FragmentManager) {
        this.summaryChildFragmentManager = fm
    }

    private fun showScenePopMenu() {
        val sceneList = getSceneList()
            .map { scene ->
                SceneItem(scene.id, scene.title, 0)
            }
        sceneBottomSheet?.dismiss()
        sceneBottomSheet = null

        sceneBottomSheet = SceneBottomSheetDialogFragment().apply {
            setMainPanelFragment(SceneDialogFragment().apply {
                // 设置生成按钮点击回调
                onGenerateClickListener = {
                    getSelectedSceneItem()?.let { item ->
                        switchTheme(item.id)
                        dismiss()
                    }
                }
                setOnDismissListener {
                    clearSceneList()
                }

                setAnimationListener(object : COUIBottomSheetDialog.OnAnimationListener {
                    override fun onDismissAnimationStart() {
                        super.onDismissAnimationStart()
                        refreshExpandIcon(false)
                    }
                })
                setData(sceneList, currentTheme?.style ?: -1)
            })
            summaryChildFragmentManager?.let { manager ->
                if (manager.isDestroyed.not()) {
                    show(manager, "SceneBottomSheet")
                } else {
                    DebugUtil.d(TAG, "FragmentManager is destroyed")
                }
            }
        }
        refreshExpandIcon(true)
    }

    private fun switchTheme(id: Int) {
        val themeChange = id != currentTheme?.style
        DebugUtil.d(TAG, "switchTheme themeChange = $themeChange, id = $id")
        if (themeChange) {
            val newTheme = SummaryTheme(id)
            summaryFunctionCallback?.onClickScene(newTheme)
        }
    }

    private fun refreshExpandIcon(isIconUp: Boolean) {
        val leftDrawable = scene.compoundDrawablesRelative[0]
        val icon = if (isIconUp) {
            sceneSelectCollapsed
        } else {
            sceneSelectExpand
        }
        scene.setCompoundDrawablesRelativeWithIntrinsicBounds(leftDrawable, null, icon, null)
    }

    private fun getSceneList(): List<PopupListItem> {
        return mutableListOf<PopupListItem>().apply {
            val builder = PopupListItem.Builder()
            //先添加识别出的场景
            DebugUtil.i(TAG, "getSceneList supportThemes = $supportThemes, identifiedTheme = $identifiedTheme")
            if (supportThemes.isEmpty()) {
                val normalDetail = SummaryTheme(SummaryTheme.NORMAL)
                builder.reset()
                builder.setId(SummaryTheme.NORMAL)
                builder.setTitle(getThemeTitle(normalDetail))
                    .setIsChecked(currentTheme == normalDetail)
                add(builder.build())
            } else {
                supportThemes.forEach {
                    val code = it.code
                    if (code == SummaryTheme.NORMAL) {
                        val normalDetail = SummaryTheme(SummaryTheme.NORMAL)
                        builder.reset()
                        builder.setId(SummaryTheme.NORMAL)
                        builder.setTitle(getThemeTitle(normalDetail)).setIsChecked(currentTheme == normalDetail)
                        add(builder.build())
                    } else {
                        val summaryTheme = SummaryTheme(code)
                        builder.reset()
                        builder.setId(code)
                        builder.setTitle(getThemeTitle(summaryTheme)).setIsChecked(currentTheme == summaryTheme)
                        add(builder.build())
                    }
                }
            }
        }
    }

    private fun clickPrevious() {
        summaryFunctionCallback?.onClickPrevious()
        refresh.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_next)
    }

    private fun clickRefreshOrNext() {
        DebugUtil.d(TAG, "clickRefreshOrNext ${refresh.tag}")
        if (refresh.tag == TAG_REFRESH) {
            summaryFunctionCallback?.onClickRefresh()
        } else {
            summaryFunctionCallback?.onClickNext()
        }
    }

    private fun clickRetry() {
        summaryFunctionCallback?.onClickRetry()
    }

    fun setSummaryFunctionCallback(callback: ISummaryFunctionCallback) {
        summaryFunctionCallback = callback
    }

    /**
     * 设置录音文件信息
     * 从SummaryFragment传递录音文件的基本信息
     */
    fun setRecordInfo(mediaId: Long, recordTitle: String, duration: Long) {
        this.currentMediaId = mediaId
        this.currentRecordTitle = recordTitle
        this.currentRecordDuration = duration
    }

    fun setSummaryContent(summaryModel: SummaryModel) {
        lastDragScrollY = 0
        onStopLoading()
        showCopyright()
        divider.visible()
        toolBar.visible()
        content.visible()
        updateScene(summaryModel.theme, true)
        updateSceneTab(currentTheme?.style ?: SummaryTheme.NORMAL)
        loadTipsGone()

        agent.clear()
        agent.addAll(summaryModel.agentEvents)
        summaryEntity.clear()
        summaryEntity.addAll(summaryModel.entities.filter { it.supportEntity() })
        val markdown = buildMarkDown()
        val formatStream = SummaryStyleHelper.formatSummaryContent(
            context,
            summaryModel.summary,
            summaryModel.summaryTrace,
            summaryEntity,
            summaryModel.agentEvents
        )
        val spanned = markdown.toMarkdown(formatStream)
        DebugUtil.d(TAG, "setSummaryContent: summaryEntity = $summaryEntity, agent = $agent")
        content.text = spanned
        summaryContentText = formatStream
        summaryOriginText = summaryModel.summary
        isLoadingFinish = true
        isFinishUploadSummary = true
        if (summaryModel.scrollToBottom) {
            scrollToBottom(true)
        }
        setContentSelectable()
    }

    private fun setContentSelectable() {
        content.setTextIsSelectable(true)
        content.movementMethod = LinkMovementMethod.getInstance()
        content.revealOnFocusHint = false
    }

    /**
     * 检测是否是最新的摘要，如果是，视觉上会有一些不同的UI
     */
    fun checkCurrentState(isLastSummary: Boolean, isOnly: Boolean, isFirstSummary: Boolean) {
        DebugUtil.d(
            TAG,
            "isLastSummary = $isLastSummary, isOnly = $isOnly, isFirstSummary = $isFirstSummary"
        )
        previous.clearFocus()
        refresh.clearFocus()
        when {
            isOnly -> {
                previous.gone()
                refresh.setImageResource(com.soundrecorder.common.R.drawable.ic_refresh)
                refresh.tag = TAG_REFRESH
            }

            isLastSummary -> {
                previous.visible()
                previous.isEnabled = true
                previous.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_perious)
                refresh.setImageResource(com.soundrecorder.common.R.drawable.ic_refresh)
                refresh.tag = TAG_REFRESH
            }

            isFirstSummary -> {
                previous.visible()
                previous.isEnabled = false
                previous.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_perious_disable)
                refresh.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_next)
                refresh.tag = TAG_NEXT
            }

            else -> {
                previous.visible()
                previous.isEnabled = true
                previous.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_perious)
                refresh.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_next)
                refresh.tag = TAG_NEXT
            }
        }
    }

    fun onStartLoading() {
        val hasContent = summaryOriginText.isNotEmpty()
        DebugUtil.d(TAG, "onStartLoading hasContent = $hasContent")
        if (hasContent) {
            return
        }
        loading()
        if (copyRight.isVisible.not()) {
            loadingJob?.cancel()
            loadingJob = lifecycle?.launch {
                delay(LOADING_TIPS_DELAY)
                DebugUtil.d(TAG, "loadingJob showLoadingTips")
                showLoadingTips()
            }
        }
    }


    fun onStopLoading() {
        stopLoading()
        loadingJob?.cancel()
        loadingJob = null
    }

    private fun loading() {
        if (loadingView.isVisible.not()) {
            loadingView.visible()
            loadingView.playAnimation()
        }
    }

    private fun stopLoading() {
        if (loadingView.isVisible) {
            loadingView.cancelAnimation()
            loadingView.gone()
        }
    }

    fun onInitSummary() {
        reset()
        loadTipsGone()
        content.gone()
        cardContainer.gone()
        copyRight.gone()
        divider.gone()
        toolBar.gone()
        scene.gone()
        tab.gone()
        errorView.gone()
        loadingView.gone()
    }

    fun onStartSummary() {
        reset()
        loadTipsGone()
        content.gone()
        content.reset()
        cardContainer.gone()
        divider.gone()
        toolBar.gone()
        scene.gone()
        tab.gone()
        errorView.gone()
        content.setTextIsSelectable(false)
        copyRightGone()
        onStartLoading()
    }

    private fun copyRightGone() {
        DebugUtil.d(TAG, "copyRightGone isLoadingTips = $isLoadingTips")
        if (isLoadingTips.not()) {
            copyRight.gone()
        }
    }

    private fun reset() {
        summaryContentText = ""
        summaryOriginText = ""
        agent.clear()
        summaryEntity.clear()
        isLoadingFinish = false
        isFinishUploadSummary = false
        supportThemes.clear()
        currentTheme = null
        identifiedTheme = SummaryTheme(SummaryTheme.NORMAL)
        lastScrollY = 0
        lastDragScrollY = 0
    }

    fun updateStream(stream: String, animator: Boolean = true, extra: Map<String, Any>? = null) {
        val isEmpty = stream.isEmpty()
        DebugUtil.d(TAG, "updateStream animator = $animator, isEmpty = $isEmpty")
        if (isEmpty) {
            return
        }
        stopLoading()
        toolBar.gone()
        divider.gone()
        errorView.gone()
        copyRight.gone()
        val isOverSize = (extra?.get(IS_OVER_SIZE) as? Boolean) ?: false
        updateLoadTips(isOverSize)
        content.visible()
        val theme = extra?.get(THEME_CODE) as? SummaryTheme
        updateScene(theme, false)
        updateSceneTab(currentTheme?.style ?: SummaryTheme.NORMAL, true)

        val markdown = buildMarkDown()
        val summaryAgent = SummaryDataParser.parseAgent(context, stream)
        val formatStream = SummaryStyleHelper.formatSummaryContent(
            context,
            stream,
            emptyList(),
            emptyList(),
            summaryAgent
        )
        val spanned = markdown.toMarkdown(formatStream)
        summaryContentText = formatStream
        summaryOriginText = stream
        if (animator) {
            content.setAnimationListener(object : TextAnimationListener {
                override fun onAnimationEnd() {
                }

                override fun onAnimationUpdate(curReadyPos: Int) {
                    super.onAnimationUpdate(curReadyPos)
                    smoothScrollToVisibleText(curReadyPos)
                }
            })
            content.setAnimateText(spanned, false)
        } else {
            content.setTextWithoutAnim(spanned)
        }
    }

    private fun getRecordFilePath(): String {
        val recordFilePath = summaryCallback?.getRecordFilePath()
        return when {
            (recordFilePath?.isNotEmpty() == true) -> recordFilePath
            else -> identifiedValue
        }
    }

    private fun getSummaryTitle(): String {
        val summaryTitle = summaryCallback?.getSummaryTitle()
        return when {
            (summaryTitle?.isNotEmpty() == true) -> summaryTitle
            currentRecordTitle.isNotEmpty() -> currentRecordTitle.title() ?: identifiedValue
            else -> identifiedValue
        }
    }

    /**
     * 如果做完动画不检验高度滚动，例如内容只有一屏的情况，会导致底部异常留白
     */

    private fun scrollToBottom(noNeedCheckHeight: Boolean) {
        DebugUtil.d(TAG, "scrollToBottom lastDragScrollY = $lastDragScrollY")
        if (lastDragScrollY > 0) return
        if (noNeedCheckHeight) {
            postDelayed(scrollToBottomRunnable, SCROLL_TO_BOTTOM_DELAY)
        } else {
            val childHeight = container.height
            DebugUtil.d(TAG, "scrollToBottom childHeight = $childHeight, height = $height")
            if (childHeight > height) {
                postDelayed(scrollToBottomRunnable, SCROLL_TO_BOTTOM_DELAY)
            }
        }
    }

    private fun smoothScrollToVisibleText(curReadyPos: Int) {
        if (lastDragScrollY > 0) return
        val layout = content.layout ?: return
        val startLine = layout.getLineForOffset(0)
        val endLine = layout.getLineForOffset(curReadyPos)
        val height = layout.getLineBottom(endLine) - layout.getLineTop(startLine)
        val scrollDistance = height + content.top + scrollToVisibleOffset - measuredHeight
        if (scrollDistance > 0) {
            smoothScrollTo(0, scrollDistance)
        }
    }

    private fun buildMarkDown(): Markwon {
        return markwon ?: run {
            val markDown = Markwon.builder(context)
                .usePlugin(
                    if (agentDrawable != null && agentUnCheckDrawable != null) {
                        TaskListPlugin.create(agentDrawable, agentUnCheckDrawable)
                    } else {
                        TaskListPlugin.create(context)
                    }
                )
                .usePlugin(SoftBreakAddsNewLinePlugin())
                .usePlugin(AgentClickPlugin(context) { isDone, task ->
                    agentClick(isDone, task)
                })
                .usePlugin(ConfigPlugin(context))
                .applyLinkResolver(SummaryLinkResolver(context) { view, entry, span ->
                    entityClick(view, entry, span)
                })

                .build()
            markwon = markDown
            markDown
        }
    }

    private fun agentClick(isDone: Boolean, task: String) {
        agent.find { it.agent == task }?.isDone = isDone
        summaryFunctionCallback?.onClickAgent(agent)
        summaryContentText = SummaryStyleHelper.updateStyleAfterTaskStateChange(summaryContentText, isDone, task)
        val markdown = buildMarkDown()
        DebugUtil.d(TAG, "agentClick = $summaryContentText")
        val spanned = markdown.toMarkdown(summaryContentText)
        content.text = spanned
    }

    private fun entityClick(view: View, entity: SummaryEntity, span: URLSpan) {
        lifecycle?.launch {
            val popupListWindow = withContext(Dispatchers.Default) {
                summaryEntityHelper.createSummaryEntityPopMenu(entity)
            }
            if (popupListWindow == null) {
                DebugUtil.e(TAG, "linkClick not support")
                return@launch
            }
            val position = summaryEntityHelper.calculateEntitySpanPosition(view, span, popupListWindow) ?: run {
                DebugUtil.e(TAG, "linkClick position is null")
                return@launch
            }
            popupListWindow.show(view, false, position[0], position[1])
        }
    }

    private fun showCopyright() {
        loadingJob?.cancel()
        copyRight.visible()
        val lp = copyRight.layoutParams as? ConstraintLayout.LayoutParams
        lp?.topToBottom = com.soundrecorder.summary.R.id.summary_content
        lp?.topMargin = context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp24)

        var copyrightRes = com.soundrecorder.base.R.string.summary_copyright
        var aiResourceRes = com.soundrecorder.summary.R.string.summary_oppo_ai
        if (BaseUtil.isOnePlus()) {
            copyrightRes = com.soundrecorder.base.R.string.summary_copyright_oplus
            aiResourceRes = com.soundrecorder.summary.R.string.summary_oplus_ai
        } else if (BaseUtil.isRealme()) {
            copyrightRes = com.soundrecorder.base.R.string.summary_copyright_realme
            aiResourceRes = com.soundrecorder.summary.R.string.summary_realme_ai
        }

        // 使用AITextStyleUtil统一处理AI文本样式
        AITextStyleUtil.setAITextStyle(
            copyRight,
            context,
            copyrightRes,
            aiResourceRes,
        )
        isLoadingTips = false
    }

    fun onTaskAlreadyRun(stream: String, extra: Map<String, Any>? = null) {
        DebugUtil.i(TAG, "onTaskAlreadyRun ${stream.isEmpty()}")
        if (stream.isEmpty()) {
            onStartSummary()
            return
        }
        updateStream(stream, animator = false, extra)
    }

    fun onFinishSummary(summaryModel: SummaryModel, animator: Boolean = true) {
        stopLoading()
        content.visible()
        updateEmptyAnim(false)
        val isOverSize = (summaryModel.extra?.get(IS_OVER_SIZE) as? Boolean) ?: false
        updateLoadTips(isOverSize)
        agent.clear()
        agent.addAll(summaryModel.agentEvents)
        summaryEntity.clear()
        summaryEntity.addAll(summaryModel.entities.filter { it.supportEntity() })
        DebugUtil.d(TAG, "onFinishSummary animator = $animator, ${summaryModel.agentEvents}, $summaryEntity")
        val markdown = buildMarkDown()
        val formatStream = SummaryStyleHelper.formatSummaryContent(
            context = context,
            originText = summaryModel.summary,
            trace = emptyList(),
            entities = summaryEntity,
            agents = summaryModel.agentEvents
        )
        val spanned = markdown.toMarkdown(formatStream)
        summaryContentText = formatStream
        summaryOriginText = summaryModel.summary
        val loadFinish = {
            content.text = spanned
            divider.visible()
            toolBar.visible()
            updateScene(summaryModel.theme, true)
            updateSceneTab(currentTheme?.style ?: SummaryTheme.NORMAL)
            isLoadingFinish = true
            setContentSelectable()
            showCopyright()
            scrollToBottom(animator.not())
            content.cancelAnimation()
            summaryFunctionCallback?.onFinishAnimator()
        }
        if (animator) {
            content.setAnimationListener(object : TextAnimationListener {
                override fun onAnimationEnd() {
                    DebugUtil.d(TAG, "onFinishSummary onAnimationEnd")
                    loadFinish.invoke()
                }

                override fun onAnimationUpdate(curReadyPos: Int) {
                    super.onAnimationUpdate(curReadyPos)
                    smoothScrollToVisibleText(curReadyPos)
                }
            })
            content.setAnimateText(spanned, false)
        } else {
            loadFinish.invoke()
        }

        isFinishUploadSummary = true
    }

    private fun updateLoadTips(isOverSize: Boolean) {
        DebugUtil.i(TAG, "updateLoadTips isOverSize = $isOverSize")
        val needShowLoadTips = isOverSize
        val msg = when {
            isOverSize -> resources.getString(com.soundrecorder.common.R.string.summary_tips_content_over_size)
            else -> ""
        }
        loadTip.setTipsText(msg)
        when {
            needShowLoadTips && loadTip.isVisible.not() -> loadTipsVisible()
            needShowLoadTips.not() && loadTip.isVisible -> loadTipsGone()
        }
    }

    private fun loadTipsVisible() {
        if (loadTip.isVisible) return
        val constraintSet = ConstraintSet()
        constraintSet.clone(container)
        constraintSet.clear(loadTip.id, ConstraintSet.TOP)
        constraintSet.connect(loadTip.id, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, contentMarginLoadTip)
        constraintSet.clear(scene.id, ConstraintSet.TOP)
        constraintSet.connect(scene.id, ConstraintSet.TOP, loadTip.id, ConstraintSet.BOTTOM, contentMarginLoadTip)
        constraintSet.applyTo(container)
        loadTip.visible()
    }

    private fun loadTipsGone() {
        if (loadTip.isGone) return
        val constraintSet = ConstraintSet()
        constraintSet.clone(container)
        constraintSet.clear(loadTip.id, ConstraintSet.TOP)
        constraintSet.clear(scene.id, ConstraintSet.TOP)
        constraintSet.connect(scene.id, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, contentMarginLoadTip)
        constraintSet.applyTo(container)
        loadTip.gone()
    }

    fun onError(canRetry: Boolean, errorMsg: String) {
        DebugUtil.d(TAG, "onError canRetry")
        onStopLoading()
        loadTipsGone()
        toolBar.gone()
        divider.gone()
        errorView.gone()
        copyRight.gone()
        content.gone()
        content.cancelAnimation()
        scene.gone()
        tab.gone()
        updateEmptyAnim(true)
        errorMsgText.text = errorMsg
        if (canRetry) {
            retry.visible()
        } else {
            retry.gone()
        }
        isLoadingFinish = true
        isFinishUploadSummary = true
        isLoadingTips = false
    }

    fun onRetryError(errorMsg: String) {
        DebugUtil.d(TAG, "onRetryError msg = $errorMsg")
        ToastManager.showShortToast(context, errorMsg)
        isFinishUploadSummary = true
    }

    fun onStop(stream: String, extra: Map<String, Any>? = null) {
        DebugUtil.d(TAG, "onStop stream ${stream.isEmpty()}")
        if (stream.isEmpty()) {
            return
        }
        onStopLoading()
        loadTipsGone()
        content.visible()
        val theme = extra?.get(THEME_CODE) as? SummaryTheme
        updateScene(theme, true)
        updateSceneTab(currentTheme?.style ?: SummaryTheme.NORMAL)
        val markdown = buildMarkDown()
        val summaryAgent = SummaryDataParser.parseAgent(context, stream)
        val formatStream = SummaryStyleHelper.formatSummaryContent(
            context,
            stream,
            emptyList(),
            emptyList(),
            summaryAgent
        )
        val spanned = markdown.toMarkdown(formatStream)
        content.text = spanned
        summaryContentText = formatStream
        summaryOriginText = stream

        divider.visible()
        toolBar.visible()
        showCopyright()
        scrollToBottom(true)

        isLoadingFinish = true
        isFinishUploadSummary = true
        setContentSelectable()
    }

    fun onCancelWarningDialog() {
        updateSceneTab(currentTheme?.style ?: SummaryTheme.NORMAL)
    }

    fun updateSummaryTrace(summaryTrace: List<SummaryTrace>) {
        DebugUtil.d(TAG, "updateSummaryTrace summaryTrace = $summaryTrace:")
    }

    fun setSummaryCallback(callback: IAISummaryCallback?) {
        summaryCallback = callback
    }

    fun isSummaryAnimator(): Boolean {
        return content.isAnimating
    }

    private fun updateEmptyAnim(isEmpty: Boolean) {
        if (isEmpty) {
            errorView.visible()
            container.viewTreeObserver.addOnGlobalLayoutListener(this)
            errorIconView.initImageResource()
        } else {
            errorView.gone()
            container.viewTreeObserver.removeOnGlobalLayoutListener(this)
        }
    }

    fun stopAnimator() {
        onStopLoading()
        content.cancelAnimation()
    }

    private fun updateScene(theme: SummaryTheme?, enable: Boolean) {
        DebugUtil.d(TAG, "updateScene theme = $theme, currentTheme = $currentTheme")
        if (theme != null && currentTheme == theme) {
            if (scene.isEnabled != enable) {
                updateSceneIcons(enable)
            }
            return
        }
        identifiedTheme = theme ?: SummaryTheme(SummaryTheme.NORMAL)
        currentTheme = when {
            ((theme?.style ?: -1) == -1) -> SummaryTheme(SummaryTheme.NORMAL)
            else -> theme
        }
        lifecycle?.launch {
            val supportTheme = withContext(Dispatchers.Default) {
                val themeInfo = PrefUtil.getString(BaseApplication.getAppContext(), SUMMARY_SUPPORT_THEME, "")
                SummaryDataParser.parseThemeSupport(themeInfo)
            }
            supportThemes.clear()
            supportThemes.addAll(supportTheme.filter { it.isSupportCode() })
            withContext(Dispatchers.Main) {
                updateSceneIcons(enable)
            }
        }
    }

    private fun getThemeTitle(theme: SummaryTheme?): String {
        theme ?: return SummaryTheme.getTitle(context, SummaryTheme.NORMAL)
        when {
            (theme.style == SummaryTheme.NORMAL
                    || theme.style == SummaryTheme.NORMAL_SIMPLE) -> return SummaryTheme.getTitle(context, SummaryTheme.NORMAL)

            else -> {
                val localLanguage = LanguageUtil.getLocalLanguage()
                val names = supportThemes.find { it.code == theme.style }?.name
                    ?: return SummaryTheme.getTitle(context, theme.style)
                return names[localLanguage] ?: SummaryTheme.getTitle(context, theme.style)
            }
        }
    }

    private fun getOriginSummaryText(): String {
        val summary = summaryOriginText.replace("*", "").replace("#", "")
        return summary
    }

    private fun setScrollState(newState: Int) {
        if (currentScrollState != newState) {
            summaryFunctionCallback?.onScrollStateChanged(newState)
        }
        currentScrollState = newState
    }

    override fun onScrollChanged(x: Int, y: Int, oldX: Int, oldY: Int) {
        super.onScrollChanged(x, y, oldX, oldY)
        // 计算滚动距离
        val dx = x - oldX
        val dy = y - oldY
        lastScrollY = y
        // 回调滚动距离变化
        summaryFunctionCallback?.onScrolled(dx, dy)
        when (currentScrollState) {
            Constants.SCROLL_STATE_SETTLING -> checkSettlingState()
            Constants.SCROLL_STATE_DRAGGING -> lastDragScrollY = y
        }
    }

    private fun checkSettlingState() {
        // 移除之前的检测任务
        removeCallbacks(scrollStateChecker)
        // 检查是否停止滚动
        postDelayed(scrollStateChecker, SCROLLER_CHECK_TIME)
    }

    private val scrollStateChecker = Runnable {
        val currentY = scrollY
        if (lastScrollY == currentY) {
            setScrollState(Constants.SCROLL_STATE_IDLE)
        } else {
            lastScrollY = currentY
            checkSettlingState()
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(ev: MotionEvent): Boolean {
        gestureDetector?.onTouchEvent(ev)
        when (ev.action) {
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> setScrollState(Constants.SCROLL_STATE_SETTLING)
        }
        return super.onTouchEvent(ev)
    }

    override fun onGlobalLayout() {
        container.viewTreeObserver.removeOnGlobalLayoutListener(this)
        if (errorView.isVisible) {
            errorIconView.setScaleByEmptySize(
                activity.px2dp(container.width).toInt(),
                activity.px2dp(container.height).toInt(),
                "onGlobalLayout"
            )
        }
    }

    fun setPaddingBottom(paddingBottom: Int) {
        toolBar.updateLayoutParams {
            height = toolbarDefaultHeight + paddingBottom
        }
    }
}