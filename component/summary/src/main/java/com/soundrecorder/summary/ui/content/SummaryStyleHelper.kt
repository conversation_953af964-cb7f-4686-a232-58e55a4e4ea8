/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryStyleHelper
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content

import android.content.Context
import android.net.Uri
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.ext.durationInMsFormatTimeExclusive
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.db.PictureMarkDbUtils
import com.soundrecorder.common.db.PictureMarkDbUtils.getPicMarkSaveDirPath
import com.soundrecorder.summary.R
import com.soundrecorder.summary.data.SummaryDataParser
import com.soundrecorder.summary.model.SummaryAgentEvent
import com.soundrecorder.summary.model.SummaryEntity
import com.soundrecorder.summary.model.SummaryTrace
import com.soundrecorder.summary.request.SummaryRecordParamGetter.getConvertSavePath
import com.soundrecorder.summary.util.SummaryConditionChecker
import com.soundrecorder.summary.util.SummaryConditionChecker.grantUriPermission
import org.json.JSONArray
import org.json.JSONObject
import java.io.BufferedReader
import java.io.BufferedWriter
import java.io.File
import java.io.InputStreamReader
import java.io.OutputStreamWriter
import java.util.regex.Pattern

@Suppress("StringTemplate")
object SummaryStyleHelper {
    private const val TAG = "SummaryStyleHelper"
    private const val LIST_SYMBOL = "\n   - "
    private const val LIST_SYMBOL_REPLACE = "\n * "
    private const val TASK_SYMBOL = "- [ ] "
    private const val TASK_DONE_SYMBOL = "- [x] "
    private const val LINE_BREAKS = "\n"

    private val taskListPattern = Pattern.compile("^- \\[([xX\\s])]\\s+.*")
    private const val SECONDS_TO_MILLS = 1000 // 秒转毫秒
    private const val TOTAL_SIZE = 7 // lrc每一项总字段数
    private const val NO_MARK = 0 //是否有标记  默认0没有， 1是有
    private const val HAS_MARK = 1 //是否有标记  默认0没有， 1是有
    private const val TIME_START = 0L // 录音开始时的时间点

    private const val INDEX_0 = 0
    private const val INDEX_1 = 1
    private const val INDEX_2 = 2
    private const val INDEX_5 = 5
    private const val INDEX_6 = 6

    private const val LRC_DATA_UNKNOWN_KEY_S = "s" // lrc数据key : s (原有AI语音摘记生成的，不知道含义)
    private const val LRC_DATA_UNKNOWN_VALUE_S = 1 // lrc数据value : 1 (原有AI语音摘记生成的，s的值初始为1)
    private const val LRC_DATA_UNKNOWN_KEY_L = "l" // lrc数据key : l (列表)
    private const val LRC_DATA_UNKNOWN_KEY_L_KEY_K = "k" // lrc数据key : l内的key (原有AI语音摘记生成的，不知道含义)
    private const val LRC_DATA_UNKNOWN_KEY_L_KEY_E = "e" // lrc数据key : 结束时间
    private const val LRC_DATA_UNKNOWN_KEY_L_KEY_N = "n" // lrc数据key : 讲话人
    private const val LRC_DATA_UNKNOWN_KEY_L_KEY_C = "c" // lrc数据key : 录音内容
    private const val LRC_DATA_UNKNOWN_KEY_L_KEY_P = "p" // lrc数据key : 图片标记
    private const val LRC_DATA_UNKNOWN_KEY_L_KEY_M = "m" // lrc数据key : 普通标记
    private const val LRC_DATA_UNKNOWN_KEY_L_KEY_S = "s" // lrc数据key : 开始时间
    private const val LRC_DATA_UNKNOWN_KEY_L_KEY_P_I = "i" // lrc数据key : uri
    private const val LRC_DATA_UNKNOWN_KEY_L_KEY_P_N = "n" // lrc数据key : 标记名称

    @JvmStatic
    fun formatSummaryContent(
        context: Context,
        originText: String,
        trace: List<SummaryTrace>,
        entities: List<SummaryEntity>,
        agents: List<SummaryAgentEvent>,
    ): String {
        val newTextWithTaskList =
            formatSummaryContentByTask(context, originText, agents)
        val newTextWithTrace = formatSummaryContentByTrace(newTextWithTaskList, trace)
        val newTextWithSummaryEntity =
            formatSummaryContentBySummaryEntity(newTextWithTrace, entities)
        return newTextWithSummaryEntity
    }

    @JvmStatic
    private fun formatSummaryContentByTrace(
        originText: String,
        traces: List<SummaryTrace>
    ): String {
        if (traces.isEmpty()) {
            return originText
        }
        val replacements = traces.map {
            it.traceText to traceFormat(it)
        }
        var result = originText
        for ((target, append) in replacements) {
            result = result.replace(target, target + append)
        }
        return result
    }

    @JvmStatic
    private fun traceFormat(trace: SummaryTrace): String {
        val time = trace.startTime.durationInMsFormatTimeExclusive()
        val entityObject = GsonUtil.getGson().toJson(trace)
        return "[`$time`](#$entityObject)"
    }

    @JvmStatic
    private fun formatSummaryContentBySummaryEntity(
        originText: String,
        originEntities: List<SummaryEntity>
    ): String {
        val entities = originEntities
        if (entities.isEmpty()) {
            return originText
        }
        val taskListItemPattern = taskListPattern
        val pattern = "(?:" + entities.joinToString("|") { "\\Q${it.name}\\E" } + ")"
        val linkMap = entities.associate { it.name to linkFormat(it) }
        val lines = originText.split(LINE_BREAKS)
        val transformedLines = lines.map { line ->
            val matcher = taskListItemPattern.matcher(line)
            if (matcher.find()) {
                line
            } else {
                Regex(pattern).replace(line) { matchResult ->
                    val name = matchResult.value
                    linkMap.getOrDefault(name, line)
                }
            }
        }
        return transformedLines.joinToString(LINE_BREAKS)
    }

    @JvmStatic
    private fun linkFormat(entity: SummaryEntity): String {
        val entityObject = GsonUtil.getGson().toJson(entity)
            .replace("\\s+".toRegex(), "")
            .replace("\\n".toRegex(), "")
        return "[${entity.name}]($entityObject)"
    }

    @JvmStatic
    private fun formatSummaryContentByTask(
        context: Context,
        originText: String,
        agents: List<SummaryAgentEvent>
    ): String {
        if (agents.isEmpty()) {
            return originText
        }
        val agentStart = getAgentStart(context, originText) ?: return originText
        val startIndex = originText.indexOf(agentStart)
        if (startIndex == -1) {
            return originText
        }
        val endIndex = getAgentEnd(context, originText, startIndex + agentStart.length)?.let {
            originText.indexOf(it, startIndex + agentStart.length)
        } ?: -1
        val subStringB = if (endIndex == -1) {
            originText.substring(startIndex + agentStart.length)
        } else {
            originText.substring(startIndex + agentStart.length, endIndex)
        }
        val lines = subStringB.split(LINE_BREAKS)
        val processedLines = lines.mapNotNull { line ->
            val cleanedLine = line.trimStart { it !in 'a'..'z' && it !in 'A'..'Z' && it !in '\u4e00'..'\u9fa5' }
            if (cleanedLine.isEmpty()) {
                null
            } else {
                val agent = agents.singleOrNull {
                    val formatAgent = SummaryDataParser.replaceEntity(cleanedLine)
                    it.agent == formatAgent
                }
                agent?.let { formatTask(agent.isDone, cleanedLine) } ?: run { null }
            }
        }
        val result =
            originText.substring(
                0,
                startIndex
            ) + agentStart + LINE_BREAKS + processedLines.joinToString(LINE_BREAKS)
        return result
    }

    @JvmStatic
    private fun formatTask(isDone: Boolean, agent: String): String {
        return if (isDone) {
            "$TASK_DONE_SYMBOL$agent"
        } else {
            "$TASK_SYMBOL$agent"
        }
    }

    @JvmStatic
    fun updateStyleAfterTaskStateChange(originText: String, isDone: Boolean, task: String): String {
        val checkboxUnchecked = "$TASK_SYMBOL$task"
        val checkboxChecked = "$TASK_DONE_SYMBOL$task"
        val updated = if (isDone && originText.contains(checkboxUnchecked)) {
            checkboxChecked
        } else if (isDone.not() && originText.contains(checkboxChecked)) {
            checkboxUnchecked
        } else {
            task
        }
        return originText.replace(checkboxUnchecked, updated).replace(checkboxChecked, updated)
    }

    @JvmStatic
    suspend fun prepareHtmlNoteContent(
        summaryOriginText: String,
        agent: List<SummaryAgentEvent>?,
        summaryEntity: List<SummaryEntity>?,
        uri: Uri,
        recordTitle: String,
        context: Context
    ): String {
        DebugUtil.d(TAG, "prepareHtmlNoteContent: summaryOriginText length=${summaryOriginText.length}, " +
                    "agent size=${agent?.size}, entity size=${summaryEntity?.size}, uri=$uri")

        // 处理待办事项剔除
        val processedText = if (!agent.isNullOrEmpty()) {
            removeTaskContentFromSummary(context, summaryOriginText)
        } else {
            summaryOriginText
        }

        // 转换为HTML格式
        val htmlContent = formatSummaryContentAsHtml(processedText)

        // 添加待办事项HTML
        val htmlWithTasks = if (!agent.isNullOrEmpty()) {
            addTasksAsHtml(htmlContent, agent)
        } else {
            htmlContent
        }

        return if (SummaryConditionChecker.isNoteSupportAudio(context)) {
            // 添加录音文件附件
            buildFileAttachmentHtml(htmlWithTasks, uri, recordTitle)
        } else {
            htmlWithTasks
        }
    }

    /**
     * 从摘要原文中剔除待办事项内容，保留"待办事项"标题
     */
    @JvmStatic
    fun removeTaskContentFromSummary(context: Context, originText: String): String {
        val agentStart = getAgentStart(context, originText) ?: return originText
        val startIndex = originText.indexOf(agentStart)
        if (startIndex == -1) {
            return originText
        }
        val endIndex = getAgentEnd(context, originText, startIndex + agentStart.length)?.let {
            originText.indexOf(it, startIndex + agentStart.length)
        } ?: -1
        return if (endIndex == -1) {
            originText.substring(0, startIndex + agentStart.length)
        } else {
            originText.substring(0, startIndex + agentStart.length) + originText.substring(endIndex)
        }
    }

    @JvmStatic
    fun getAgentStart(context: Context, originText: String): String? {
        val agentStartList = listOf(
            R.string.summary_agent,
            R.string.summary_agent_1,
            R.string.summary_agent_action,
            R.string.summary_agent_export,
            R.string.summary_agent_export_1,
            R.string.summary_agent_export_action,
            R.string.summary_agent_export_2,
            R.string.summary_agent_export_action_1
        )
        val agent = agentStartList.firstOrNull { originText.indexOf(context.getString(it)) > -1 } ?: return null
        return context.getString(agent)
    }

    @JvmStatic
    fun getAgentEnd(context: Context, originText: String, start: Int): String? {
        val agentEndList = listOf(
            R.string.summary_agent_end,
            R.string.summary_agent_export_end,
            R.string.summary_agent_export_end_1,
            R.string.summary_agent_export_end_2
        )
        val agentEnd = agentEndList.firstOrNull {
            originText.substring(start).indexOf(context.getString(it)) > -1
        } ?: return null
        return context.getString(agentEnd)
    }

    /**
     * 将摘要内容转换为HTML格式
     * 处理标题、数字开头的文本、短横线开头的文本
     */
    @JvmStatic
    private fun formatSummaryContentAsHtml(originText: String): String {
        val cleanText = originText.replace("*", "")
        val lines = cleanText.split("\n").map { it.trim() }.filter { it.isNotEmpty() }
        val htmlBuilder = StringBuilder()
        htmlBuilder.append("<html><body>")

        // 使用函数式编程方法处理行，避免while循环
        processLinesAsHtml(lines, htmlBuilder)

        // 关闭HTML标签
        htmlBuilder.append("</body></html>")
        return htmlBuilder.toString()
    }

    /**
     * 处理文本行并转换为HTML格式
     * 使用递归和函数式方法避免while循环
     */
    @JvmStatic
    private fun processLinesAsHtml(lines: List<String>, htmlBuilder: StringBuilder) {
        if (lines.isEmpty()) return

        val groupedLines = groupConsecutiveLines(lines)

        groupedLines.forEach { group ->
            when (group.type) {
                LineType.DASH_LIST -> {
                    // 处理连续的短横线列表项
                    htmlBuilder.append("<ul>\n")
                    group.lines.forEach { item ->
                        val content = item.substring(1).trim() // 移除开头的"-"
                        htmlBuilder.append("  <li>").append(content).append("</li>\n")
                    }
                    htmlBuilder.append("</ul>\n")
                }
                LineType.NUMBERED_TITLE -> {
                    // 处理编号标题
                    group.lines.forEach { line ->
                        htmlBuilder.append("<ul>").append(line).append("</ul>\n")
                    }
                }
                LineType.REGULAR_TITLE -> {
                    // 处理普通标题
                    group.lines.forEach { line ->
                        htmlBuilder.append("<ul>").append(line).append("</ul>\n")
                    }
                }
                LineType.DEFAULT -> {
                    // 处理默认段落
                    group.lines.forEach { line ->
                        htmlBuilder.append("<ul>").append(line).append("</ul>\n")
                    }
                }
            }
        }
    }

    /**
     * 行类型枚举
     */
    private enum class LineType {
        DASH_LIST,      // 短横线列表项
        NUMBERED_TITLE, // 编号标题
        REGULAR_TITLE,  // 普通标题
        DEFAULT         // 默认段落
    }

    /**
     * 行分组数据类
     */
    private data class LineGroup(
        val type: LineType,
        val lines: List<String>
    )

    /**
     * 将连续的相同类型行分组
     * 使用函数式编程方法，避免复杂的循环逻辑
     */
    @JvmStatic
    private fun groupConsecutiveLines(lines: List<String>): List<LineGroup> {
        if (lines.isEmpty()) return emptyList()

        val groups = mutableListOf<LineGroup>()
        var currentGroup = mutableListOf<String>()
        var currentType: LineType? = null

        lines.forEach { line ->
            val lineType = determineLineType(line)

            if (currentType == null) {
                // 第一行，初始化
                currentType = lineType
                currentGroup.add(line)
            } else if (currentType == lineType && lineType == LineType.DASH_LIST) {
                // 连续的短横线列表项，继续添加到当前组
                currentGroup.add(line)
            } else {
                // 类型变化或非短横线列表项，结束当前组并开始新组
                val type = currentType
                if (currentGroup.isNotEmpty() && type != null) {
                    groups.add(LineGroup(type, currentGroup.toList()))
                }
                currentGroup = mutableListOf(line)
                currentType = lineType
            }
        }

        // 添加最后一组
        val type = currentType
        if (currentGroup.isNotEmpty() && type != null) {
            groups.add(LineGroup(type, currentGroup.toList()))
        }

        return groups
    }

    /**
     * 确定行的类型
     * 使用纯函数方法，避免复杂的条件判断
     */
    @JvmStatic
    private fun determineLineType(line: String): LineType {
        return when {
            line.startsWith("-") -> LineType.DASH_LIST
            line.matches(Regex("^\\d+\\.\\s*.*")) -> LineType.NUMBERED_TITLE
            !line.matches(Regex("^\\d+\\..*")) && !line.startsWith("-") -> LineType.REGULAR_TITLE
            else -> LineType.DEFAULT
        }
    }

    /**
     * 在HTML内容中添加待办事项的checkbox格式
     */
    @JvmStatic
    private fun addTasksAsHtml(htmlContent: String, agents: List<SummaryAgentEvent>): String {
        if (agents.isEmpty()) {
            DebugUtil.d(TAG, "addTasksAsHtml: agents is empty, returning original content")
            return htmlContent
        }

        val taskHtml = StringBuilder()
        taskHtml.append("<ul>\n")
        agents.forEachIndexed { index, agent ->
            DebugUtil.d(TAG, "addTasksAsHtml: processing agent[$index]: ${agent.agent}")
            val checked = if (agent.isDone) { "checked" } else { "unchecked" }
            taskHtml.append("  <li class=\"$checked\">").append(agent.agent).append("</li>\n")
        }
        taskHtml.append("</ul>\n")

        val result = htmlContent.replace("</body></html>", "$taskHtml </body></html>")
        DebugUtil.d(TAG, "addTasksAsHtml: result length=${result.length}, original length=${htmlContent.length}")
        return result
    }

    /**
     * 在markdown内容中添加待办事项的checkbox格式
     */
    @JvmStatic
    fun addToDoAsMarkdown(markdownContent: String, agents: List<SummaryAgentEvent>): String {
        if (agents.isEmpty()) {
            DebugUtil.d(TAG, "addTasksAsHtml: agents is empty, returning original content")
            return markdownContent
        }

        val taskHtml = StringBuilder()
        taskHtml.append("\n")
        agents.forEachIndexed { index, agent ->
            DebugUtil.d(TAG, "addTasksAsMarkdown: processing agent[$index]: ${agent.agent}")
            val mdCheckBox = if (agent.isDone) "- [x] " else "- [ ] "
            taskHtml.append(mdCheckBox).append(agent.agent).append("\n")
        }

        val result = markdownContent + taskHtml
        DebugUtil.d(TAG, "addTasksAsMarkdown: result length=${result.length}, original length=${markdownContent.length}")
        return result
    }

    /**
     * 将实体转换为指定json格式
     * @param entityListJson 实体集合json
     * @return 便签接口需要的格式
     * @see "https://odocs.myoas.com/docs/1d3aVlX7nZC0Dyqg?lang=zh-CN&zone=%2B8&tt_versionCode=800&mobileMsgShowStyle=1&pcMsgShowStyle=1"
     * */
    fun convertEntityToJson(entityListJson: String, record: Record): String {
        val jsonArray = JSONArray(entityListJson)
        val result = JSONObject()
        val entities = JSONArray()
        // 安全遍历JSONArray的推荐方式
        for (i in 0 until jsonArray.length()) {
            val item = jsonArray.optJSONObject(i) ?: continue
            // 时间戳为-1的不处理
            if (item.optLong("timestampForNote", -1).toInt() == -1) {
                continue
            }
            entities.put(JSONObject().apply {
                put("index_end", item.optInt("indexEnd", -1))
                put("name", item.optString("name", ""))
                put("index_start", item.optInt("indexStart", -1))
                put("summaryindex", item.optInt("summaryIndex", -1))
                //这里要将时间戳减去record的创建时间毫秒级得到相对于原文的开始时间，see:com/soundrecorder/summary/request/SummaryRecordParamGetter.kt
                val timestamp = if ((item.optLong("timestampForNote", -1) == -1L)) {
                    -1
                } else {
                    (item.optLong("timestampForNote", -1) - record.dateCreated * SECONDS_TO_MILLS).toString()
                }
                put("timestamp", timestamp.toString())
                put("turnId", item.optInt("turnId", -1))
                put("type", item.optString("type", ""))
            })
        }
        result.put("entities", entities)
        return result.toString()
    }

    /**
     * 将现有ASR生成的文本内容数据源转换为指定的json格式
     * @see /data/data/com.coloros.soundrecorder/files/convert
     * */
    fun convertLrcToJsonObject(input: String): JSONObject {
        val jsonObject = JSONObject()
        val lines = input.trim().split("\n")
        val itemsArray = JSONArray()

        jsonObject.put(LRC_DATA_UNKNOWN_KEY_S, LRC_DATA_UNKNOWN_VALUE_S)

        for (line in lines) {
            if (line.isBlank()) continue

            val parts = line.split("/")
            if (parts.size < TOTAL_SIZE) continue

            val item = JSONObject()
            item.put(LRC_DATA_UNKNOWN_KEY_L_KEY_S, parts[INDEX_0].toLong() / SECONDS_TO_MILLS)
            item.put(LRC_DATA_UNKNOWN_KEY_L_KEY_E, parts[INDEX_1].toLong() / SECONDS_TO_MILLS)
            item.put(LRC_DATA_UNKNOWN_KEY_L_KEY_N, parts[INDEX_6])
            item.put(LRC_DATA_UNKNOWN_KEY_L_KEY_C, parts[INDEX_2])
            item.put(LRC_DATA_UNKNOWN_KEY_L_KEY_K, parts[INDEX_5])
            item.put(LRC_DATA_UNKNOWN_KEY_L_KEY_M, NO_MARK) //0默认没标记
            item.put(LRC_DATA_UNKNOWN_KEY_L_KEY_P, JSONArray()) // []默认没图片

            itemsArray.put(item)
        }

        jsonObject.put(LRC_DATA_UNKNOWN_KEY_L, itemsArray)
        return jsonObject
    }

    /**
     * 从指定uri中读取内容
     * */
    @Synchronized
    fun readContentFromUri(context: Context, uri: Uri): String? {
        return runCatching {
            context.contentResolver.openInputStream(uri)?.use { inputStream ->
                BufferedReader(InputStreamReader(inputStream)).use { reader ->
                    reader.readText()
                }
            }
        }.onFailure { e ->
            DebugUtil.w(TAG, "readContentFromUri failed: $e")
        }.getOrNull()
    }


    /**
     * 创建临时文件并写入内容
     * @param context 上下文对象
     * @param fileName 文件名
     * @param content 写入内容
     * @return 文件绝对路径，失败时返回""
     */
    @Synchronized
    fun writeToFileWithPath(
        context: Context,
        fileName: String,
        content: String
    ): String {
        return runCatching {
            val dir = File(getConvertSavePath(context)).apply {
                if (!exists()) mkdirs()
            }
            File(dir, fileName).apply {
                BufferedWriter(OutputStreamWriter(outputStream())).use {
                    it.write(content)
                }
            }.absolutePath
        }.getOrDefault("")
    }

    /**
     * @param jsonObject:文本内容json
     * 创建临时文件,并写入内容
     * */
    fun processVoiceLrcFile(
        context: Context,
        jsonObject: JSONObject,
        keyId: Long
    ): JSONObject {
        val queryPictureMarks = PictureMarkDbUtils.queryPictureMarks(keyId.toString())
        val speechArray = jsonObject.getJSONArray(LRC_DATA_UNKNOWN_KEY_L)

        for (i in INDEX_0 until speechArray.length()) {
            val isFirstItem = i == INDEX_0
            val speechItem = speechArray.getJSONObject(i)
            val prevItem = if (!isFirstItem) speechArray.getJSONObject(i - 1) else null

            processMarkForItem(
                context = context,
                markList = queryPictureMarks,
                currentItem = speechItem,
                prevItem = prevItem,
                isFirstItem = isFirstItem
            )
        }
        return jsonObject
    }

    private fun processMarkForItem(
        context: Context,
        markList: List<MarkDataBean>,
        currentItem: JSONObject,
        prevItem: JSONObject?,
        isFirstItem: Boolean
    ) {
        val currentItemStartTime = currentItem.getLong(LRC_DATA_UNKNOWN_KEY_L_KEY_S)
        val currentItemEndTime = currentItem.getLong(LRC_DATA_UNKNOWN_KEY_L_KEY_E)
        val prevItemEndTime = prevItem?.getLong(LRC_DATA_UNKNOWN_KEY_L_KEY_E) ?: 0L
        for (mark in markList) {
            when {
                isFirstItem && mark.timeInMills == TIME_START -> {
                    checkPictureMark(context, mark, currentItem)
                    currentItem.put(LRC_DATA_UNKNOWN_KEY_L_KEY_M, HAS_MARK)
                    return
                }
                !isFirstItem && mark.timeInMills in prevItemEndTime..currentItemStartTime -> {
                    checkPictureMark(context, mark, prevItem)
                    prevItem?.put(LRC_DATA_UNKNOWN_KEY_L_KEY_M, HAS_MARK)
                    return
                }
                mark.timeInMills in currentItemStartTime..currentItemEndTime -> {
                    checkPictureMark(context, mark, currentItem)
                    currentItem.put(LRC_DATA_UNKNOWN_KEY_L_KEY_M, HAS_MARK)
                    return
                }
                else -> currentItem.put(LRC_DATA_UNKNOWN_KEY_L_KEY_M, NO_MARK)
            }
        }
    }

    /**
     * 检查mark里面是否有图片标记，有的话加入到speechItem里面
     * @param mark
     * @param speechItem lrc的一项
     * */
    private fun checkPictureMark(context: Context, mark: MarkDataBean, speechItem: JSONObject?) {
        mark.pictureFilePath.takeIf { it.isNotBlank() }?.let { path ->
            val pArray = speechItem?.optJSONArray(LRC_DATA_UNKNOWN_KEY_L_KEY_P) ?: JSONArray().also {
                speechItem?.put(LRC_DATA_UNKNOWN_KEY_L_KEY_P, it)
            }
            val imageObj = JSONObject().apply {
                val markPicturesPath = getPicMarkSaveDirPath(context) + File.separator + path
                val uri = grantUriPermission(context, markPicturesPath)
                put(LRC_DATA_UNKNOWN_KEY_L_KEY_P_I, uri)
                put(LRC_DATA_UNKNOWN_KEY_L_KEY_P_N, mark.markText)
            }
            if (!pArray.toString().contains(path)) {
                pArray.put(imageObj)
            }
        }
    }

    /**
     * 构建录音文件附件的HTML
     */
    @JvmStatic
    private fun buildFileAttachmentHtml(htmlContent: String, uri: Uri, recordTitle: String): String {
        val attachmentHtml = "<div><input type='audio' src='$uri' alt='$recordTitle' /></div>"
        return htmlContent.replace("</body></html>", "$attachmentHtml</body></html>").also {
            DebugUtil.d(TAG, "buildFileAttachmentHtml: result length=${it.length}, original length=${htmlContent.length}")
        }
    }
}