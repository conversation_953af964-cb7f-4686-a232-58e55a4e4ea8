/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryModel
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.model

import android.content.Context
import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.JumpIntentUtil
import com.soundrecorder.summary.R
import com.soundrecorder.summary.model.SummaryTheme.Companion.CLASSROOM
import com.soundrecorder.summary.model.SummaryTheme.Companion.INTERVIEW
import com.soundrecorder.summary.model.SummaryTheme.Companion.MEETING
import com.soundrecorder.summary.model.SummaryTheme.Companion.NORMAL
import com.soundrecorder.summary.model.SummaryTheme.Companion.PHONE
import com.soundrecorder.summary.request.database.SummaryCacheEntity
import kotlinx.parcelize.Parcelize
import java.lang.reflect.Type

//无效的录音
const val INVALID_MEDIA = -100

//网络异常
const val NOTWORK_ERROR = -101

//aiunit相关错误
const val LOAD_ERROR = -103

//aiunit相关错误
const val AIUNIT_ERROR = -2

//参数错误；语种不支持
const val INVALID_PARAM = 100001

//鉴权失败
const val AUTH_FAILED = 100002

//触发内容安全
const val RISK_CONTENT = 100003

//language is not support
const val LANGUAGE_NOT_SUPPORT = 100004

//输入长度超限
const val CONTENT_LENGTH_EXCEEDED = 100005

//AI模型服务异常，如google的接口异常，超过并发等;
const val AI_SERVICE_ERROR = 200001

//the llm is stop Generate content
const val AI_SERVICE_STOP = 200002
const val AI_SERVICE_TOKEN_EXCEED = 200003

//插件未初始化或者已被销毁需要重新初始化
const val PLUGIN_INIT_ERROR = 100008

//插件任务执行超时
const val REQUEST_TIMEOUT = 100009

//网络不可达
const val NETWORK_ERROR = 100010

//#文件解析失败
const val FILE_PARSE_FAILED = 100011

//##溯源文档摘要：文档在解析中或者向量化中
const val FILE_NOT_PREPARED = 100012

//录音内容较少，智能命名失败
const val CONTENT_LESS_ERROR = 100013

//server exception
const val SERVER_ERROR = 100014

//## 获取主题失败
const val LLM_GET_THEME_FAILED = 100015

//## 主题类型不支持
const val LLM_THEME_NOT_SUPPORT = 100016

//## 主题模板不支持
const val SUMMARY_THEME_NOT_SUPPORT = 100020
//用户输入没有任何实际含义，或者绝大部分篇幅充斥无意义字符，不支持生成摘要
const val SUMMARY_CONTENT_MEANINGLESS = 100021

//语音摘要内部错误
const val VOICE_SUMMARY_INNER_ERROR = 300000

//输入错误 # Unsupported language
const val UNSUPPORTED_LANGUAGE = 300001

//llm服务错误
const val LLM_CLIENT_ERROR = 300002

//ner服务内部错误
const val NER_CLIENT_ERROR = 300003

//无法生成标题
const val AI_TITLE_SUPPORT_ERROR = 300004

//内销文本类摘要错误 #摘要服务内部错误
const val LLM_SUMMARY_INNER_ERROR = 400000

const val SMART_EXPCETION = 60000
const val FILE_SIZE_UNSUPPORT_ZERO = 60001
const val FILE_SIZE_MAX_UNSUPPORT = 60002
const val DURATION_MIN_UNSUPPORT_ZERO = 60003
const val DURATION_MAX_UNSUPPORT = 60004
const val FILE_FORMAT_UNSUPPORT = 60005
const val FILE_NOT_EXIST = 60006
const val CONVERT_TEXT_UNSUPPORT = 60007

// 通话摘要笔记本相关常量
const val NOTE_VALUE_CALL_SUMMARY = 0 // 通话摘要
const val NOTE_VALUE_CALL_RECORD_SUMMARY = 1      // 录音-通话录音摘要
const val NOTE_VALUE_QQ_CALL_RECORD_SUMMARY = 2   // 录音-QQ通话录音摘要
const val NOTE_VALUE_WECHAT_CALL_RECORD_SUMMARY = 3 // 录音-微信通话录音摘要
const val NOTE_VALUE_QQ_TRANSCRIPT_SUMMARY = 4    // 语音转文字-QQ通话录音摘要
const val NOTE_VALUE_WECHAT_TRANSCRIPT_SUMMARY = 5 // 语音转文字-微信通话录音摘要
// 语音摘要笔记本相关常量
const val NOTE_VALUE_NON_CALL_RECORD_SUMMARY = 6  // 录音-非通话录音摘要
const val NOTE_VALUE_MEETING_TRANSCRIPT_SUMMARY = 7 // 语音转文字-会议软件
const val NOTE_VALUE_VIDEO_TRANSCRIPT_SUMMARY = 8 // 语音转文字-视频软件
const val NOTE_VALUE_OTHER_TRANSCRIPT_SUMMARY = 9 // 语音转文字-其它应用
// 文章摘要笔记本相关常量
const val NOTE_VALUE_ARTICLE_SUMMARY = 10         // 文章摘要
const val NOTE_VALUE_FULL_DOCUMENT_SUMMARY = 11   // 全文摘要（文档）
/**
 * 数据示例
 * 总结：范先生作为中信银行信用卡中心的业务代表，向何魏先生推荐了一笔额外的现金贷款服务。\n核心事件：\n1.范先生表示何魏先生的信用记录良好，因此向他提供了额外的一笔现金贷款。\n2.何魏先生拒绝了这项服务，因为他没有资金需求，并且对中信银行的信用卡体验不佳。\n待办事项：\n1.范先生可能需要记录下何魏先生的反馈，以便后续改进服务或调整营销策略。
 */
@Keep
data class SummaryModel(

    val id: Long,

    val mediaId: Long,

    /**
     * 总结
     */
    val summary: String,

    /**
     * 待办事件
     */
    val agentEvents: List<SummaryAgentEvent>,

    /**
     * 实体，可点击
     */
    val entities: List<SummaryEntity>,

    /**
     * 溯源
     */
    val summaryTrace: List<SummaryTrace>,

    /**
     * 主题
     */
    val theme: SummaryTheme? = null,

    /**
     * 摘要的时间
     */
    val summaryTime: Long,

    val title: String? = null,

    val language: String? = null,

    val extra: HashMap<String, Any>? = null,

    var scrollToBottom: Boolean = false
)

/**
 * 待办事件
 */
@Keep
data class SummaryAgentEvent(
    /**
     * 待办描述
     */
    val agent: String,

    /**
     * 是否完成
     */
    var isDone: Boolean,
)

/**
 * 摘要实体
 * 示例：
 * [
 * 	{
 * 		"name": "范先生",
 * 		"turnId": null,
 * 		"index_start": null,
 * 		"index_end": null,
 * 		"timestamp": null,
 * 		"type": "人名",
 * 		"summaryindex": 3
 * 	},
 * 	{
 * 		"name": "范先生",
 * 		"turnId": null,
 * 		"index_start": null,
 * 		"index_end": null,
 * 		"timestamp": null,
 * 		"type": "人名",
 * 		"summaryindex": 52
 * 	}
 *]
 */
@Keep
data class SummaryEntity(
    val name: String,
    val turnId: Int,
    val indexStart: Int,
    val indexEnd: Int,
    val timestamp: Long,
    val type: String,
    val summaryIndex: Int,
    val timestampForNote: Long = -1, // 新增给摘要导出给便签接口的
) {
    companion object {
        const val SCHEDULE = "时间"
        const val ADDRESS = "地址"
        const val EXPRESS = "express_delivery"
        const val PHONE = "phone_number"
        const val EMAIL = "email"
        const val WEB = "url"
        const val TIME = "Time" //外销时间
        const val EXPORT_ADDRESS = "Address" //外销地址
        const val NO_TIME_SCHEDULE = "年月日" //用于只有日期的实体
        //留下做测试，先不删
        const val NAME = "人名" //用于只有日期的实体

        const val TIME_INVALID = -1L

        @JvmStatic
        fun typeTime(type: String): Boolean {
            return type == SCHEDULE || type == TIME || type == NO_TIME_SCHEDULE
        }
    }

    fun supportEntity(): Boolean {
        val typeIdRight = (type == ADDRESS
                || type == EXPRESS
                || type == EMAIL
                || type == EXPORT_ADDRESS)
        val timeType = typeTime(type)
        val isPhone = type == PHONE
        val isWeb = type == WEB

        val phoneNumberIsRight = isPhone && JumpIntentUtil.isValidPhoneNumber(name)
        val webUrlIsRight = isWeb && JumpIntentUtil.isValidUrl(name)
        val timeIsRight = timeType && timestamp != TIME_INVALID

        return (typeIdRight && name.isNotEmpty()) || phoneNumberIsRight || webUrlIsRight || timeIsRight
    }
}

@Keep
data class SummaryTrace(
    /**
     * 录音文本开始时间
     */
    val startTime: Long,

    /**
     * 录音文本结束时间
     */
    val endTime: Long,

    /**
     * 匹配的原文
     */
    val chunkText: String,

    /**
     * 溯源的文本
     */
    val traceText: String
)

@Keep
data class SummaryTheme(
    /**
     * 识别的主题
     */
    val style: Int = -1
) {
    companion object {
        const val NORMAL_SIMPLE = -100
        const val NORMAL = 1000
        const val PHONE = 1001
        const val MEETING = 1002
        const val CLASSROOM = 1003
        const val INTERVIEW = 1004
        const val CONFERENCE = 1005
        const val PAPERS = 1006

        @JvmStatic
        fun getTitle(context: Context, style: Int): String {
            return when (style) {
                NORMAL_SIMPLE -> context.getString(com.soundrecorder.common.R.string.summary_style_sample)
                NORMAL -> context.getString(com.soundrecorder.common.R.string.summary_template_standard)
                PHONE -> context.getString(com.soundrecorder.common.R.string.summary_style_phone_call)
                MEETING -> context.getString(com.soundrecorder.common.R.string.summary_style_meeting)
                CLASSROOM -> context.getString(com.soundrecorder.common.R.string.summary_style_course)
                INTERVIEW -> context.getString(com.soundrecorder.common.R.string.summary_style_interviews)
                else -> context.getString(com.soundrecorder.common.R.string.summary_style_detail)
            }
        }
        @JvmStatic
        fun getDrawableRes(style: Int): Int {
            return when (style) {
                NORMAL -> R.drawable.ic_normal_small
                MEETING -> R.drawable.ic_meeting_small
                PHONE -> R.drawable.ic_phone_small
                INTERVIEW -> R.drawable.ic_interview_small
                CLASSROOM -> R.drawable.ic_classroom_small
                else -> R.drawable.ic_normal_small
            }
        }
    }
}

@Keep
data class SummarySupportTheme(
    val code: Int,
    val name: Map<String, String>
) {
    fun isSupportCode(): Boolean {
        return code == NORMAL
                || code == PHONE
                || code == MEETING
                || code == CLASSROOM
                || code == INTERVIEW
    }
}

/**
 * stream
 */
@Keep
data class SummaryStream(
    val stream: String,
    val extra: Map<String, Any>? = null
)

/**
 * error
 */
@Keep
data class SummaryError(
    val errorCode: Int,
    val errorMsgRes: Int,
    val canRetry: Boolean
)

@Keep
data class SummaryCountModel(
    val currentPosition: Int,
    val count: Int,
)

@Keep
data class SummaryStop(
    val reason: Int,
) {
    companion object {
        //正常结束
        const val REASON_FINISH = 1
        //用户停止
        const val REASON_STOP = 2
        //错误结束
        const val REASON_ERROR = 3
        //重试错误结束
        const val REASON_RETRY_ERROR = 4
    }
}

fun SummaryCacheEntity.toSummaryModel(): SummaryModel {
    val summaryContent = this.summaryContent ?: return SummaryModel(
        this.id,
        this.mediaId,
        summary = "",
        emptyList(),
        emptyList(),
        emptyList(),
        SummaryTheme(-1),
        -1
    )
    val summaryAgent = kotlin.runCatching {
        val type: Type = object : TypeToken<List<SummaryAgentEvent>>() {}.type
        GsonUtil.getGson().fromJson<List<SummaryAgentEvent>>(this.summaryAgent, type) ?: emptyList()
    }.onFailure {
        DebugUtil.e("SummaryModel", "loadSummaryFromDataBase agent ${this.summaryAgent}")
    }.getOrDefault(emptyList())

    val summaryEntity = kotlin.runCatching {
        val type: Type = object : TypeToken<List<SummaryEntity>>() {}.type
        GsonUtil.getGson().fromJson<List<SummaryEntity>>(this.summaryEntity, type) ?: emptyList()
    }.onFailure {
        DebugUtil.e("SummaryModel", "loadSummaryFromDataBase entity ${this.summaryEntity}")
    }.getOrDefault(emptyList())

    val summaryTrace = kotlin.runCatching {
        val type: Type = object : TypeToken<List<SummaryTrace>>() {}.type
        GsonUtil.getGson().fromJson<List<SummaryTrace>>(this.summaryTrace, type) ?: emptyList()
    }.onFailure {
        DebugUtil.e("SummaryModel", "loadSummaryFromDataBase trace ${this.summaryTrace}")
    }.getOrDefault(emptyList())

    val theme = SummaryTheme(this.summaryStyle)
    val time = this.timeStamp
    return SummaryModel(
        this.id,
        this.mediaId,
        summaryContent,
        summaryAgent,
        summaryEntity,
        summaryTrace,
        theme,
        time,
        title = this.summaryTitle,
        language = this.summaryLanguage
    )
}

/**
 * 便签接口需要的参数格式
 * @see "https://odocs.myoas.com/docs/1d3aVlX7nZC0Dyqg?lang=zh-CN&zone=%2B8&tt_versionCode=800&mobileMsgShowStyle=1&pcMsgShowStyle=1" 10.笔记插入
 * */
@Parcelize
data class Extra(
    @Expose @SerializedName("speech_type") var speechType: Int = -1,
    @Expose @SerializedName("voice_file") var voiceFile: String? = null,
    @Expose @SerializedName("speech_log_id") var speechLogId: String? = null,
    @Expose @SerializedName("contact_cover") var contactCover: String? = null,
    @Expose @SerializedName("contact_number") var contactNumber: String? = null,
    @Expose @SerializedName("contact_name") var contactName: String? = null,
    @Expose @SerializedName("speech_start_time") var speechStartTime: Long? = null,
    @Expose @SerializedName("speech_abnormal_status") var speechAbnormalStatus: Int? = null,
    @Expose @SerializedName("audio_extra") var audioExtra: String? = null,
    @Expose @SerializedName("app_extra") var appExtra: String? = null,
    @Expose @SerializedName("voice_lrc_file") var voiceLrcFile: String? = null,
    @Expose @SerializedName("speech_count") var speechCount: Int? = null,
    @Expose @SerializedName("speech_short") var speechShort: Boolean? = null,
    @Expose @SerializedName("lrc_status") var lrcStatus: Int? = null,
    @Expose @SerializedName("entity_status") var entityStatus: Int? = null,
    @Expose @SerializedName("entity_combination_status") var entityCombinationStatus: Int? = null,
    @Expose @SerializedName("entity") var entity: String? = null,
    @Expose @SerializedName("entityCombination") var entityCombination: String? = null,
    @Expose @SerializedName("summaryResultText") var summaryResultText: String? = null,
    @Expose @SerializedName("model_type") var modelType: Int = -1,
    @Expose @SerializedName("asrUUId") var asrUUId: String? = null
) : Parcelable

/**
 * 便签接口需要的参数格式
 * */
@Keep
data class AudioExtra(
    var fileName: String? = null,
    var filePath: String? = null,
    var md5: String? = null,
    var mediaId: String? = null
)