/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryExportToNote
 * * Description :  摘要管理器实现类，负责处理摘要相关的业务逻辑
 * * Version     : 1.0
 * * Date        : 2025/07/14
 * * Author      : AI Assistant
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content

import android.app.Activity
import android.content.ContentResolver
import android.content.ContentValues
import android.content.Context
import android.net.Uri
import android.view.View
import androidx.appcompat.content.res.AppCompatResources
import androidx.lifecycle.LifecycleCoroutineScope
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.google.gson.reflect.TypeToken
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_ACTION_EXPORT
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_EXPORT_NOTE
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_FORMAT_NOTE
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_RETURN_FAILURE
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_RETURN_SUCCESS
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_STICKER_INSTALLATION_CANCELLED
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_STICKER_INSTALLATION_FAILED
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_TYPE_SUMMARY
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.exportfile.SummaryContentViewUtil
import com.soundrecorder.common.exportfile.ui.ExportTipsManager
import com.soundrecorder.common.note.ExportNoteUtil
import com.soundrecorder.common.removableapp.InstallResultCallback
import com.soundrecorder.common.removableapp.RemovableAppManager
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.RecordModeUtil.recordType
import com.soundrecorder.summary.model.AudioExtra
import com.soundrecorder.summary.model.Extra
import com.soundrecorder.summary.model.NOTE_VALUE_CALL_RECORD_SUMMARY
import com.soundrecorder.summary.model.NOTE_VALUE_NON_CALL_RECORD_SUMMARY
import com.soundrecorder.summary.model.SummaryAgentEvent
import com.soundrecorder.summary.model.SummaryEntity
import com.soundrecorder.summary.request.database.SummaryCacheDBHelper
import com.soundrecorder.summary.ui.content.SummaryStyleHelper.addToDoAsMarkdown
import com.soundrecorder.summary.ui.content.SummaryStyleHelper.convertEntityToJson
import com.soundrecorder.summary.ui.content.SummaryStyleHelper.convertLrcToJsonObject
import com.soundrecorder.summary.ui.content.SummaryStyleHelper.processVoiceLrcFile
import com.soundrecorder.summary.ui.content.SummaryStyleHelper.readContentFromUri
import com.soundrecorder.summary.ui.content.SummaryStyleHelper.removeTaskContentFromSummary
import com.soundrecorder.summary.ui.content.SummaryStyleHelper.writeToFileWithPath
import com.soundrecorder.summary.util.SummaryConditionChecker
import com.soundrecorder.summary.util.SummaryConditionChecker.grantUriPermission
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.lang.ref.WeakReference
import java.lang.reflect.Type
import java.util.UUID

object SummaryExportToNote {
    private const val TAG = "SummaryExportToNote"
    private const val NOTE_KEY_EXTRA = "extra"
    private const val NOTE_KEY_MARKDOWN_CONTENT = "markdown_content"
    private const val NOTE_KEY_AI_SUMMARY = "ai_summary"
    private const val NOTE_KEY_PACKAGE_NAME = "package_name"
    private const val NOTE_KEY_TITLE = "title"
    private const val NOTE_KEY_HTML_CONTENT = "html_content"
    private const val DELAY_TIME_INSERT_TO_NOTE = 500L
    private val notePackageName = AppUtil.getNotesPackageName()

    /**
     * 导出请求参数封装
     * @param context 上下文
     * @param mediaId 录音文件ID
     * @param recordTitle 录音标题
     * @param recordFilePath 录音文件路径
     */
    data class ExportRequest(
        val context: Context,
        val mediaId: Long,
        val recordTitle: String? = null,
        val recordFilePath: String? = null
    )

    /**
     * SummaryContentView调用相关参数封装
     */
    data class ContentViewParams(
        val lifecycle: LifecycleCoroutineScope,
        val exportTipsManager: ExportTipsManager,
        val viewRef: WeakReference<View>
    ) {
        /**
         * 安全获取View实例
         * @return View实例，如果已被回收则返回null
         */
        fun getView(): View? = viewRef.get()
    }

    /**
     * 导出成功处理参数封装
     */
    data class ExportSuccessParams(
        val context: Context,
        val mediaId: Long,
        val insertUri: Uri,
        val grantedUri: Uri,
        val tempUri: Uri,
        val showSnackBar: Boolean = false,
        val contentViewParams: ContentViewParams? = null
    )

    /**
     * 导出摘要到便签 - 统一入口方
     */
    fun exportSummaryToNote(
        context: Context,
        mediaId: Long,
        recordTitle: String?,
        recordFilePath: String?
    ) {
        val exportRequest = ExportRequest(
            context = context,
            mediaId = mediaId,
            recordTitle = recordTitle,
            recordFilePath = recordFilePath
        )
        exportSummaryToNoteInternal(exportRequest)
    }

    /**
     * 导出摘要到便签 - SummaryContentView专用方法
     * @param exportRequest 导出请求参数
     * @param contentViewParams SummaryContentView相关参数
     */
    fun exportSummaryToNoteFromContentView(
        exportRequest: ExportRequest,
        contentViewParams: ContentViewParams
    ) {
        exportSummaryToNoteInternal(exportRequest, contentViewParams)
    }

    /**
     * 导出摘要到便签 - 内部实现方法（带ContentView参数）
     */
    private fun exportSummaryToNoteInternal(
        exportRequest: ExportRequest,
        contentViewParams: ContentViewParams? = null
    ) {
        val scope = contentViewParams?.lifecycle ?: CoroutineScope(Dispatchers.IO)
        scope.launch(Dispatchers.IO) {
            runCatching {
                val noteData = buildNoteData(exportRequest.context, exportRequest.mediaId, exportRequest.recordTitle, exportRequest.recordFilePath)
                if (noteData == null) {
                    return@launch
                }

                val install = SummaryContentViewUtil.isNoteAppInstalled(exportRequest.context as Activity)
                val insertUri = ExportNoteUtil.insertToNote(exportRequest.context, noteData.values)

                withContext(Dispatchers.Main) {
                    if (install && insertUri != null) {
                        val successParams = ExportSuccessParams(
                            context = exportRequest.context,
                            insertUri = insertUri,
                            grantedUri = noteData.grantedUri,
                            tempUri = noteData.tempUri,
                            mediaId = exportRequest.mediaId,
                            showSnackBar = contentViewParams != null,
                            contentViewParams = contentViewParams
                        )
                        handleExportSuccess(successParams)
                    } else {
                        handleExportFailure(exportRequest, noteData.grantedUri)
                    }
                }
            }.onFailure { exception ->
                DebugUtil.e(TAG, "exportSummaryToNote error: ${exception.message}")
            }
        }
    }

    /**
     * 便签数据类
     */
    private data class NoteData(
        val values: ContentValues,
        val grantedUri: Uri,
        val tempUri: Uri,
    )

    /**
     * 构建便签数据 - 内部实现
     */
    @Suppress("LongMethod")
    private fun buildNoteData(
        context: Context,
        mediaId: Long,
        recordTitle: String?,
        recordFilePath: String?
    ): NoteData? {
        val summaryCacheEntity = SummaryCacheDBHelper.getChooseSummaryByMediaId(mediaId)
        if (summaryCacheEntity == null) {
            DebugUtil.w(TAG, "buildNoteData: no summary found for mediaId=$mediaId")
            return null
        }
        // 优先使用传入的参数，否则查询数据库
        val (noteTitle, filePath) = when {
            recordTitle.isNullOrBlank() || recordFilePath.isNullOrBlank() -> {
                val result = MediaDBUtils.queryRecordById(mediaId) ?: return null
                (recordTitle ?: result.displayName) to (recordFilePath ?: result.data)
            }

            else -> recordTitle to recordFilePath
        }

        val grantedUri = SummaryConditionChecker.grantUriPermission(context, filePath)

        val agentEvents = parseAgentEvents(summaryCacheEntity.summaryAgent)

        val entities = parseEntities(summaryCacheEntity.summaryEntity).filter { it.supportEntity() }
        // 判断录音类型
        val recordData = MediaDBUtils.queryRecordById(mediaId)
        DebugUtil.d(TAG, "buildNoteData recordData: $recordData")
        val speechType =
            if (recordData.relativePath.recordType() == RecordModeConstant.RECORD_TYPE_CALL) {
                NOTE_VALUE_CALL_RECORD_SUMMARY
            } else {
                NOTE_VALUE_NON_CALL_RECORD_SUMMARY
            }
        DebugUtil.d(
            TAG,
            "buildNoteData speechType: $speechType recordData.relativePath.recordType() ${recordData.relativePath.recordType()}"
        )
        /*voice_lrc_file处理开始*/
        // 通过mediaId获取当前asr转换记录
        val keyId = RecorderDBUtil.getKeyIdByPath(filePath)
        val recordDataLocal = RecorderDBUtil.getInstance(context).qureyRecordByPath(filePath)
        DebugUtil.d(TAG, "buildNoteData recordDataLocal: $recordDataLocal")
        val summaryData = NoteDbUtils.queryNoteByMediaId(mediaId.toString()) // 摘要表
        val convertRecord = ConvertDbUtil.selectByRecordId(mediaId) // asr转换记录表
        // 通过asr记录获取生成的txt完整路径并生成授权的lrc uri
        val lrcUri =
            convertRecord?.convertTextfilePath?.toString()?.let { grantUriPermission(context, it) }
        // 通过授权后的uri读取里面的asr内容，并转成老版本录音的AI语音摘记生成的lrc的JSON格式
        val jsonObj = if (lrcUri != null) {
            val originLrcString = readContentFromUri(context, lrcUri)
            DebugUtil.d(
                TAG,
                "buildNoteData origin voice lrc file content: ${originLrcString.toString()}"
            )
            originLrcString?.let { convertLrcToJsonObject(it) }
        } else {
            null
        }
        // 由于新版本生成的lrc没有包含标记，需要单独将标记添加到生成的jsonObj中
        val finalJsonObj = jsonObj?.let { processVoiceLrcFile(context, it, keyId.toLong()) }
        DebugUtil.d(TAG, "buildNoteData voice lrc file content: ${finalJsonObj.toString()}")
        // 由于convert下面的lrc文件不能修改内容，所以临时生成一个txt文件，将上面的finalJsonObj数据写入到临时txt
        val tempPath =
            writeToFileWithPath(context, "temp_$recordTitle.txt", finalJsonObj.toString())
        // 通过临时txt文件的完整路径获取uri并授权
        val tempUri = grantUriPermission(context, tempPath)
        /*voice_lrc_file处理结束*/

        // 添加待办
        val mdContentWithAgents = summaryCacheEntity.summaryContent?.let {
            if (agentEvents.isNotEmpty()) removeTaskContentFromSummary(context, it) else it
        }?.let { addToDoAsMarkdown(it, agentEvents) }
        DebugUtil.d(TAG, "buildNoteData agent $mdContentWithAgents")
        // 组织数据
        val extra = Extra().apply {
            audioExtra = GsonUtil.toJson(AudioExtra().apply {
                fileName = "$noteTitle.mp3"
                this.filePath = filePath
                this.mediaId = mediaId.toString()
                this.md5 = recordDataLocal.mMD5
            })
            this.entity = convertEntityToJson(GsonUtil.toJson(entities), recordDataLocal)
            DebugUtil.d(TAG, "buildNoteData entity ${this.entity} ")
            voiceFile = grantedUri.toString()
            speechLogId = recordDataLocal?.uuid ?: UUID.randomUUID().toString()
            this.modelType = 1
            this.voiceLrcFile = tempUri.toString()
            this.speechType = speechType
            // 非通话摘要传应用名，通话摘要传联系人
            this.contactName = if (speechType == NOTE_VALUE_CALL_RECORD_SUMMARY) {
                recordDataLocal.callerName
                    ?: context.getString(com.soundrecorder.common.R.string.app_name_main)
            } else {
                context.getString(com.soundrecorder.common.R.string.app_name_main)
            }
            DebugUtil.d(TAG, "buildNoteData contactName ${this.contactName} ")
        }

        val json = GsonUtil.toJson(extra)
        DebugUtil.d(TAG, "buildNoteData extra: $json")
        // 准备便签数据
        val values = ContentValues().apply {
            put(NOTE_KEY_PACKAGE_NAME, context.packageName)
            put(NOTE_KEY_TITLE, noteTitle)
            put(NOTE_KEY_EXTRA, json)
            put(NOTE_KEY_MARKDOWN_CONTENT, mdContentWithAgents)
            put(NOTE_KEY_AI_SUMMARY, true)
        }
        DebugUtil.d(TAG, "buildNoteData values: $values")
        return NoteData(values, grantedUri, tempUri)
    }

    /**
     * 处理导出成功
     */
    private fun handleExportSuccess(successParams: ExportSuccessParams) {
        val result = deleteFileByUri(successParams.context, successParams.tempUri)
        DebugUtil.d(TAG, "deleteFileByUri result: $result")
        // 撤销URI权限
        SummaryConditionChecker.revokeUriPermission(successParams.context, successParams.grantedUri)
        if (successParams.showSnackBar && successParams.contentViewParams != null) {
            // SummaryContentView调用：显示完整的导出流程UI
            val contentViewParams = successParams.contentViewParams
            contentViewParams.exportTipsManager.showExportProcessDialog(
                successParams.context,
                com.soundrecorder.common.R.string.is_saving
            ) {
                val currentView = contentViewParams.getView()
                if (currentView != null) {
                    contentViewParams.exportTipsManager.showSnackBar(
                        successParams.context,
                        currentView,
                        successParams.context.getString(
                            com.soundrecorder.common.R.string.export_store_loacl_tips,
                            successParams.context.getString(com.soundrecorder.common.R.string.install_note_name)
                        ),
                        successParams.context.getString(com.soundrecorder.common.R.string.export_view_look)
                    ) {
                        SummaryContentViewUtil.jumpToNote(successParams.insertUri, successParams.context as Activity)
                    }
                }
            }
        } else {
            // ExportHelper调用：直接跳转到便签
            SummaryContentViewUtil.jumpToNote(successParams.insertUri, successParams.context as Activity)
        }

        // 记录成功埋点
        AISummaryBuryingUtil.addRecordShareEvent(
            AISummaryBuryingUtil.ShareEventInfo(
                successParams.mediaId.toString(), VALUE_TYPE_SUMMARY, VALUE_ACTION_EXPORT,
                VALUE_EXPORT_NOTE, VALUE_FORMAT_NOTE, VALUE_RETURN_SUCCESS, ""
            )
        )
    }

    /**
     * 处理导出失败
     */
    private fun handleExportFailure(exportRequest: ExportRequest, grantedUri: Uri) {
        // 撤销URI权限
        SummaryConditionChecker.revokeUriPermission(exportRequest.context, grantedUri)
        // 显示便签应用安装对话框
        showInstallNotesDialog(exportRequest)
    }

    /**
     * 解析待办事项JSON字符串
     */
    private fun parseAgentEvents(agentJson: String?): List<SummaryAgentEvent> {
        if (agentJson.isNullOrEmpty()) {
            return emptyList()
        }
        return runCatching {
            val type: Type = object : TypeToken<List<SummaryAgentEvent>>() {}.type
            GsonUtil.getGson().fromJson<List<SummaryAgentEvent>>(agentJson, type) ?: emptyList()
        }.onFailure {
            DebugUtil.e(TAG, "parseAgentEvents error: ${it.message}, json=$agentJson")
        }.getOrDefault(emptyList())
    }

    /**
     * 解析实体JSON字符串
     */
    private fun parseEntities(entityJson: String?): List<SummaryEntity> {
        if (entityJson.isNullOrEmpty()) {
            return emptyList()
        }
        return runCatching {
            val type: Type = object : TypeToken<List<SummaryEntity>>() {}.type
            GsonUtil.getGson().fromJson<List<SummaryEntity>>(entityJson, type) ?: emptyList()
        }.onFailure {
            DebugUtil.e(TAG, "parseEntities error: ${it.message}, json=$entityJson")
        }.getOrDefault(emptyList())
    }

    /**
     * 显示便签应用安装对话框
     */
    private fun showInstallNotesDialog(exportRequest: ExportRequest) {
        val title = String.format(
            exportRequest.context.getString(com.soundrecorder.common.R.string.summary_install_note),
            exportRequest.context.getString(com.soundrecorder.common.R.string.install_note_name)
        )
        COUIAlertDialogBuilder(exportRequest.context, com.support.dialog.R.style.COUIAlertDialog_Custom)
            .setTitle(title)
            .setCustomMessage(exportRequest.context.getString(com.soundrecorder.common.R.string.summary_install_note_content))
            .setCustomDrawable(AppCompatResources.getDrawable(exportRequest.context, com.soundrecorder.summary.R.drawable.ic_note))
            .setPositiveButton(com.soundrecorder.common.R.string.install) { _, _ ->
                reInstallNote(exportRequest.context, exportRequest.mediaId, exportRequest.recordTitle, exportRequest.recordFilePath)
            }
            .setNegativeButton(com.soundrecorder.common.R.string.cancel) { _, _ ->
                AISummaryBuryingUtil.addRecordShareEvent(
                    AISummaryBuryingUtil.ShareEventInfo(
                        exportRequest.mediaId.toString(), VALUE_TYPE_SUMMARY, VALUE_ACTION_EXPORT,
                        VALUE_EXPORT_NOTE, VALUE_FORMAT_NOTE, VALUE_RETURN_FAILURE, VALUE_STICKER_INSTALLATION_CANCELLED
                    )
                )
            }
            .show()
    }

    /**
     * 使用系统应用找回功能重新安装便签应用
     */
    private fun reInstallNote(
        context: Context,
        mediaId: Long,
        recordTitle: String?,
        recordFilePath: String?,
    ) {
        runCatching {
            val removableAppManager = RemovableAppManager(notePackageName)
            // 获取便签应用信息
            val removableAppInfo = removableAppManager.obtainRemovableInfo()
            // 执行重新安装
            removableAppManager.reInstallApp(
                context, removableAppInfo, callback = object : InstallResultCallback {
                    override fun onInstalledResult(success: Boolean) {
                        DebugUtil.d(TAG, "Note app reinstallation result: $success")
                        // 在主线程中处理结果
                        CoroutineScope(Dispatchers.Main).launch {
                            if (success) {
                                // 安装成功，重新尝试导出
                                handleReinstallSuccess(context, mediaId, recordTitle, recordFilePath)
                            } else {
                                // 安装失败，显示错误信息
                                handleReinstallFailure(context, mediaId)
                            }
                        }
                    }
                },
                negative = {
                    // 用户在安装过程中取消
                    DebugUtil.d(TAG, "User cancelled note app installation")
                }
            )
        }.onFailure {
            DebugUtil.e(TAG, "Error during note app reinstallation: ${it.message}")
            // 如果系统应用找回失败，回退到原有的应用恢复列表逻辑
            SummaryContentViewUtil.jumpAppRecoverList(context)
        }
    }

    /**
     * 处理重新安装成功
     */
    private suspend fun handleReinstallSuccess(
        context: Context,
        mediaId: Long,
        recordTitle: String?,
        recordFilePath: String?,
    ) {
        withContext(Dispatchers.IO) {
            // 重新构建便签数据
            val noteData = buildNoteData(context, mediaId, recordTitle, recordFilePath)
            if (noteData == null) {
                DebugUtil.w(TAG, "handleReinstallSuccess: failed to build note data")
                return@withContext
            }

            val install = SummaryContentViewUtil.isNoteAppInstalled(context as Activity)
            delay(DELAY_TIME_INSERT_TO_NOTE)
            val insertUri = ExportNoteUtil.insertToNote(context, noteData.values)

            if (install && insertUri != null) {
                withContext(Dispatchers.Main) {
                    SummaryContentViewUtil.jumpToNote(insertUri, context)
                    val result = deleteFileByUri(context, noteData.tempUri)
                    DebugUtil.d(TAG, "deleteFileByUri result: $result")
                    SummaryConditionChecker.revokeUriPermission(context, noteData.grantedUri)
                    AISummaryBuryingUtil.addRecordShareEvent(
                        AISummaryBuryingUtil.ShareEventInfo(
                            mediaId.toString(), VALUE_TYPE_SUMMARY,
                            VALUE_ACTION_EXPORT, VALUE_EXPORT_NOTE, VALUE_FORMAT_NOTE, VALUE_RETURN_SUCCESS, ""
                        )
                    )
                }
            } else {
                // 重新安装后仍然失败
                handleReinstallFailure(context, mediaId)
            }
        }
    }

    private fun deleteFileByUri(context: Context, uri: Uri): Boolean {
        return runCatching {
            when (uri.scheme) {
                ContentResolver.SCHEME_CONTENT -> {
                    // 处理content://类型的URI
                    val rowsDeleted = context.contentResolver.delete(uri, null, null)
                    rowsDeleted > 0
                }

                ContentResolver.SCHEME_FILE -> {
                    // 处理file://类型的URI
                    uri.path?.let { path ->
                        File(path).takeIf { it.exists() }?.delete() ?: false
                    } ?: false
                }

                else -> false
            }
        }.getOrElse { exception ->
            DebugUtil.e(TAG, "deleteFileByUri fail: $exception")
            false
        }
    }

    /**
     * 处理重新安装失败
     */
    private fun handleReinstallFailure(context: Context, mediaId: Long) {
        ExportTipsManager().showExportError(context.getString(com.soundrecorder.common.R.string.second_verify_failed))
        DebugUtil.e(TAG, "handleReinstallFailure: ExportHelper caller")
        AISummaryBuryingUtil.addRecordShareEvent(
            AISummaryBuryingUtil.ShareEventInfo(
                mediaId.toString(), VALUE_TYPE_SUMMARY, VALUE_ACTION_EXPORT,
                VALUE_EXPORT_NOTE, VALUE_FORMAT_NOTE, VALUE_RETURN_FAILURE, VALUE_STICKER_INSTALLATION_FAILED
            )
        )
    }
}