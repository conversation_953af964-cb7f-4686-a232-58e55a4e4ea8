/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RealTimeAudioRecorder
 * Description:
 * Version: 1.0
 * Date: 2025/3/4
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/4 1.0 create
 */

package com.soundrecorder.recorderservice.recorder

import android.annotation.SuppressLint
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.RecorderConstant
import com.soundrecorder.common.utils.SingleThread
import com.soundrecorder.translate.util.SubtitleDebugExportHelper
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.TimeUnit

@SuppressLint("MissingPermission")
class RealTimeAudioRecorder(var listener: IRealTimeRecordListener? = null) {

    companion object {
        private const val SAMPLE_16BIT = 16
        private const val SAMPLE_8BIT = 8

        const val CHANNEL_SINGLE = 1
        const val CHANNEL_DOUBLE = 2
        private const val TIMER_INTERVAL = 40
        private const val QUEUE_TIME_OUT = 40L
        private const val TIME_RATE = 1000
        private const val SAMPLE_COUNT = 2
        private const val CHANNEL_BIT_SIZE = 8

        private const val QUEUE_CAPACITY = 1500

        private const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT
        private const val CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO
        const val SAMPLE_RATE = RecorderConstant.SAMPLE_RATE_16000

        private const val PCM_16BIT = 16
        private const val INVALID_VALUE = 0
        private const val SINGLE_CHANNEL = 1


        enum class State {
            DEFAULT, INITIALIZING, RECORDING, PAUSE, ERROR, STOPPED
        }
    }

    private val logTag = "RealTimeAudioRecorder"

    private var audioBuffer: ByteArray? = null

    @Volatile
    private var audioRecord: AudioRecord? = null

    @Volatile
    private var state = State.DEFAULT
    private var bufferSize = 0
    private var mChannels: Short = 0
    private var mSamples: Short = 0
    private val singleSendThread = SingleThread("singleSendThread")
    private val singleReadThread = SingleThread("singleReadThread")
    private var audioQueue: LinkedBlockingQueue<ByteArray> = LinkedBlockingQueue(QUEUE_CAPACITY)

    /*是否停止PCM数据写入和读取，用于切换ASR语种*/
    @Volatile
    var stopProcessData = false

    fun initRecorder() {
        runCatching {
            initAudioRecorder()
        }.onFailure {
            state = State.ERROR
        }
    }

    fun startRecord(channelId: String) {
        val audioRecord = this.audioRecord ?: run {
            state = State.ERROR
            DebugUtil.w(logTag, "mAudioRecorder is null")
            return
        }
        if (state == State.RECORDING) {
            return
        }
        val audioBuffer = this.audioBuffer ?: return
        DebugUtil.i(logTag, "startRecord")
        SubtitleDebugExportHelper.init()
        runCatching {
            audioRecord.startRecording()
            state = State.RECORDING
            val read: Int = audioRecord.read(audioBuffer, 0, bufferSize)
            if (read <= 0) {
                DebugUtil.w(logTag, "read buffer is null")
            }
        }.onFailure {
            DebugUtil.e(logTag, "startRecord $it")
            state = State.ERROR
        }
        processReadData(channelId)
    }

    fun pauseRecord() {
        if (state == State.RECORDING) {
            DebugUtil.i(logTag, "pauseRecord")
            /** 暂停要先更改状态，再调用stop，否则stop耗时，导致processReadData数据流一直读取发送*/
            state = State.PAUSE
            runCatching {
                audioRecord?.stop()
            }.onFailure {
                DebugUtil.w(logTag, "pauseRecord $it")
                state = State.ERROR
            }
        }
    }

    fun resumeRecord(channelId: String) {
        if (state == State.PAUSE) {
            DebugUtil.i(logTag, "resumeRecord")
            runCatching {
                audioRecord?.startRecording()
                state = State.RECORDING
                processReadData(channelId)
            }.onFailure {
                DebugUtil.w(logTag, "resumeRecord $it")
                state = State.ERROR
            }
        }
    }

    fun stopRecord() {
        DebugUtil.i(logTag, "stopRecord")
        if (state == State.RECORDING) {
            runCatching {
                state = State.STOPPED
                audioRecord?.stop()
            }.onFailure {
                state = State.ERROR
                DebugUtil.w(logTag, "stopRecord $it")
            }
            SubtitleDebugExportHelper.release()
        }
    }

    fun release() {
        DebugUtil.i(logTag, "release")
        runCatching {
            state = State.DEFAULT
            audioRecord?.release()
        }.onFailure {
            state = State.ERROR
            DebugUtil.w(logTag, "release $it")
        }
        audioRecord = null
        singleReadThread.clearQueue()
        singleReadThread.quitThread()
        singleSendThread.clearQueue()
        singleSendThread.quitThread()
    }

    private fun initAudioRecorder() {
        if (audioRecord == null) {
            state = State.INITIALIZING
            bufferSize = calculateRecorderBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT) / SAMPLE_COUNT
            audioRecord = AudioRecord(MediaRecorder.AudioSource.MIC, SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT, bufferSize)
            audioBuffer = ByteArray(bufferSize)
            DebugUtil.i(logTag, "initAudioRecorder, bufferSize=$bufferSize")
        }
    }

    /**
     * 计算buffer默认2倍大小
     * @param sampleRate
     * @param channelConfig
     * @param audioFormat
     * @return
     */
    private fun calculateRecorderBufferSize(sampleRate: Int, channelConfig: Int, audioFormat: Int): Int {
        mSamples = if (audioFormat == AudioFormat.ENCODING_PCM_16BIT) {
            SAMPLE_16BIT.toShort()
        } else {
            SAMPLE_8BIT.toShort()
        }
        mChannels = if (channelConfig == AudioFormat.CHANNEL_IN_MONO) {
            CHANNEL_SINGLE.toShort()
        } else {
            CHANNEL_DOUBLE.toShort()
        }
        val mFramePeriod = sampleRate * TIMER_INTERVAL / TIME_RATE
        return mFramePeriod * SAMPLE_COUNT * mSamples * mChannels / CHANNEL_BIT_SIZE
    }

    fun processReadData(channelId: String) {
        DebugUtil.i(logTag, "processReadData start")
        singleReadThread.post {
            DebugUtil.i(logTag, "processReadData: read voice start $state")
            while (isRecording() && stopProcessData.not()) {
                val newMicBuffer = ByteArray(bufferSize)
                audioRecord?.read(newMicBuffer, 0, bufferSize) ?: 0
                audioQueue.offer(newMicBuffer)
                SubtitleDebugExportHelper.export()?.writeAudioPcm(newMicBuffer)
            }
            DebugUtil.i(logTag, "processReadData: read voice end $state")
        }

        singleSendThread.post {
            DebugUtil.i(logTag, "processReadData: read queue start $state")
            while (isRecording() && stopProcessData.not()) {
                audioQueue.poll(QUEUE_TIME_OUT, TimeUnit.MILLISECONDS)?.let {
                    listener?.onAudioData(channelId, it)
                }
            }
            DebugUtil.i(logTag, "processReadData: read queue end $state, ${audioQueue.size}")
            audioQueue.clear()
            listener?.onStopProcessData(channelId, stopProcessData)
        }
    }

    fun isRecording(): Boolean = state == State.RECORDING

    fun isPaused(): Boolean = state == State.PAUSE

    fun isInitializing(): Boolean = state == State.INITIALIZING

    fun getBufferSize(): Int = bufferSize

    fun getAudioFormat(): Int = when (AUDIO_FORMAT) {
        AudioFormat.ENCODING_PCM_16BIT -> PCM_16BIT
        else -> {
            DebugUtil.i(logTag, "getAudioFormat: unKnow")
            INVALID_VALUE
        }
    }

    fun getChannelNum(): Int = when (CHANNEL_CONFIG) {
        AudioFormat.CHANNEL_IN_MONO -> SINGLE_CHANNEL
        else -> {
            DebugUtil.i(logTag, "getChannelNum: unKnow")
            INVALID_VALUE
        }
    }
}

interface IRealTimeRecordListener {
    fun onAudioData(channelId: String, data: ByteArray)
    fun onStopProcessData(channelId: String, stopProcessFlag: Boolean)
}