apply plugin: 'com.android.dynamic-feature'
apply plugin: 'com.oplus.oms.plugin.feature'
apply plugin: 'obuildplugin'

OBuildConfig {
    outputType = "apk"
    //不能直接复制，需要根据应用的渠道配置，分不同的编译组。
    buildTask = "OppoPallDomestic,OppoPallExport,OppoPallGdpr," +
            "OneplusPallDomestic,OneplusPallExport,OneplusPallGdpr"
    codeScanVariants = "OppoPallDomesticAallDebug"
    //接口测试的变体，一般是oppo内销最新版本单变体即可
    androidTestVariants = "OppoPallDomesticAallDebug"
    //单元测试的变体，一般是oppo内销最新版本单变体即可
    unitTestVariants = "OppoPallDomesticAallDebug"
}

apply plugin: 'com.oppo.plugin'

osign {
    credentials {
        username = signUserName
        password = signPassword
    }
    brand = 'PSW' // 品牌名，默认值 PSW
    baseline = 'Oplus_key' // 基线
    project = 'Oplus_prj' // 项目代号，默认值 Oplus_prj
    signType = 'oplus_app' // 签名类型，默认值 oplus_app
    signVersion = 'v3_single' // 签名种类，v3_single, v3_double, v2_single，默认值
    devEnv = false
}

configurations.compileOnly {
    // 统一排除COUI等依赖导入的冲突addon SDK版本
    exclude group: 'com.oplus.sdk', module: 'addon'
}

android {
    namespace 'com.soundrecorder.dynamic.oplusns'
    compileSdk 36

    defaultConfig {
        minSdk 29
        versionCode 105
        versionName "1.5"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        externalNativeBuild {
            cmake {
                cppFlags ""
                arguments "-DANDROID_STL=c++_shared"
                abiFilters 'arm64-v8a'
            }
        }

        ndk {
            abiFilters 'arm64-v8a'
        }
        ndkVersion "26.1.10909125"
    }

    buildFeatures {
        //  compose true
        buildConfig true
    }

    //B：品牌，如果应用不存在品牌差异，需要设置一个“ball”
    //P:产品，如果应用不存在轻量os版本差异，需要设置一个“pall”
    //地区：如果应用不存在地区差异，需要设置一个“regionall”
    //api版本：如果应用不存在colorOS版本裂化，需要设置一个“apilevelall”即可
    flavorDimensions "B", "P", "region", "apilevel"
    productFlavors {
        //apk名称需要重命名为：“OPPO”
        oppo {
            dimension "B"
        }
        //apk名称需要重命名为：“OnePlus”
        oneplus {
            dimension "B"
        }
        domestic {
            dimension "region"
        }
        export {
            dimension "region"
            //默认情况，只编译核心语言，但是服务器编译，会动态修改，编译外销需要的所有语言,如没有配置，会是全语言
            if (prop_disableSubPackage.toBoolean()) {
                println("app disable resource subpacakge")
            } else {
                if (!prop_exp_resConfig.toString().isEmpty()) {
                    resConfigs prop_exp_resConfig
                } else {
                    println("subpacakge config is empty, no subpackage")
                }
            }
        }
        //apk名称需要重命名为：“GDPR”
        gdpr {
            dimension "region"
            //默认情况，只编译核心语言，但是服务器编译，会动态修改，编译外销需要的所有语言,如没有配置，会是全语言
            if (prop_disableSubPackage.toBoolean()) {
                println("app disable resource subpacakge")
            } else {
                if (!prop_exp_resConfig.toString().isEmpty()) {
                    resConfigs prop_exp_resConfig
                } else {
                    println("subpacakge config is empty, no subpackage")
                }
            }
        }
        aall {
            dimension "apilevel"
        }
        pall {
            dimension "P"
        }
    }

    buildTypes {
        //access jacoco coverage
        coverage {
            initWith(buildTypes.release)
            matchingFallbacks = ['release']
            //coverage预插桩，执行测试用例才能统计代码函数覆盖率。
            //config coverage enabled true
            //此处area必须为空, 维护人（孟占军、凌奋）
            manifestPlaceholders = [area:""]
            //需要disabel原因是：若混淆瘦身，将无法统计函数覆盖率。且coverage出包，仅用于覆盖率测试。
            //config disable proGuard
            minifyEnabled false
            //config disable shrink resources
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            shrinkResources false
        }
        release {
            minifyEnabled false
        }
        oapm {
            initWith(buildTypes.release)
            matchingFallbacks = ['release']
            manifestPlaceholders = [area: ""]
        }
    }

    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
            version "3.22.1"
        }
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['src/main/jniLibs']
        }
    }
    compileOptions {
        sourceCompatibility prop_targetCompatibility
        targetCompatibility prop_targetCompatibility
    }
    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }
}

//有些版本编译没有意义，这里过滤掉，提升编译速度，必须小写的
//这段代码不能直接抄，需要自己思考
android.variantFilter { variant ->
    def buildTypeName = variant.buildType.name
    def flavor = variant.getFlavors().name
    def fullFlavorName = flavor + ":" + buildTypeName
    //只输出oapm OPPO全量版本
    if (fullFlavorName.contains("oapm")) {
        if (!fullFlavorName.contains("oppo")) {
            variant.setIgnore(true)
        }
    }
    //其他可以忽略的变体，应用自己梳理

    //只输出 coverage 品牌内销版本
    if (fullFlavorName.contains("coverage")) {
        //如果没有轻量os，需要删除full判断；如果没有版本裂化，需要删除aall判断的
        if (!fullFlavorName.contains("oppo") || !fullFlavorName.contains("domestic")) {
            variant.setIgnore(true)
        }
    }
}

tasks.configureEach { task ->
    if (task.name.contains("omsCompare") && task.name.endsWith("ManifestAndDependency")) {
        task.enabled = false
    }
}

OmsFeature {
    // 是否预置到系统组件分区
    isComponentFeature = false
    // 配置插件运行的独立进程
    // process = ':local'
    // 如有端云需求，code 需要配置, code 获取参考
    // https://odocs.myoas.com/docs/Wr3DVz2eOKCY2YkJ?mobileMsgShowStyle=1&pcMsgShowStyle=1
    productConfig {
        oppo {
            infoForSplit = '{code:51281920}'
        }
        oneplus {
            infoForSplit = '{code:51281920}'
        }
        realme {
            infoForSplit = '{code:51281920}'
        }
    }
}

dependencies {
    implementation project(":app")
    implementation libs.androidx.annotation.jvm
    implementation project(':component:audioEffectApi')
    implementation libs.androidx.appcompat
    implementation libs.material
    implementation libs.androidx.activity
    implementation libs.androidx.constraintlayout
}