                        -HF:\project\NewSoundRecord\4\NewSoundRecord\component\plugin\plugin_oplus_ns\src\main\cpp
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=29
-DANDROID_PLATFORM=android-29
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=D:\Users\W9096060\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DCMAKE_ANDROID_NDK=D:\Users\W9096060\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DCMAKE_TOOLCHAIN_FILE=D:\Users\W9096060\AppData\Local\Android\Sdk\ndk\26.1.10909125\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\Users\W9096060\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\project\NewSoundRecord\4\NewSoundRecord\component\plugin\plugin_oplus_ns\build\intermediates\cxx\Debug\3d3f313j\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\project\NewSoundRecord\4\NewSoundRecord\component\plugin\plugin_oplus_ns\build\intermediates\cxx\Debug\3d3f313j\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-BF:\project\NewSoundRecord\4\NewSoundRecord\component\plugin\plugin_oplus_ns\.cxx\Debug\3d3f313j\arm64-v8a
-GNinja
-DANDROID_STL=c++_shared
                        Build command args: []
                        Version: 2