[{"directory": "F:/project/NewSoundRecord/4/NewSoundRecord/component/plugin/plugin_oplus_ns/.cxx/Debug/3d3f313j/arm64-v8a", "command": "D:\\Users\\W9096060\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android29 --sysroot=D:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DOplusNS_EXPORTS -IF:/project/NewSoundRecord/4/NewSoundRecord/component/plugin/plugin_oplus_ns/src/main/cpp/inc -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -o CMakeFiles\\OplusNS.dir\\OplusNS.cpp.o -c F:\\project\\NewSoundRecord\\4\\NewSoundRecord\\component\\plugin\\plugin_oplus_ns\\src\\main\\cpp\\OplusNS.cpp", "file": "F:\\project\\NewSoundRecord\\4\\NewSoundRecord\\component\\plugin\\plugin_oplus_ns\\src\\main\\cpp\\OplusNS.cpp"}, {"directory": "F:/project/NewSoundRecord/4/NewSoundRecord/component/plugin/plugin_oplus_ns/.cxx/Debug/3d3f313j/arm64-v8a", "command": "D:\\Users\\W9096060\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android29 --sysroot=D:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DOplusNS_EXPORTS -IF:/project/NewSoundRecord/4/NewSoundRecord/component/plugin/plugin_oplus_ns/src/main/cpp/inc -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -o CMakeFiles\\OplusNS.dir\\managed_jnienv.cpp.o -c F:\\project\\NewSoundRecord\\4\\NewSoundRecord\\component\\plugin\\plugin_oplus_ns\\src\\main\\cpp\\managed_jnienv.cpp", "file": "F:\\project\\NewSoundRecord\\4\\NewSoundRecord\\component\\plugin\\plugin_oplus_ns\\src\\main\\cpp\\managed_jnienv.cpp"}, {"directory": "F:/project/NewSoundRecord/4/NewSoundRecord/component/plugin/plugin_oplus_ns/.cxx/Debug/3d3f313j/arm64-v8a", "command": "D:\\Users\\W9096060\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android29 --sysroot=D:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DOplusNS_EXPORTS -IF:/project/NewSoundRecord/4/NewSoundRecord/component/plugin/plugin_oplus_ns/src/main/cpp/inc -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -o CMakeFiles\\OplusNS.dir\\bridge.cpp.o -c F:\\project\\NewSoundRecord\\4\\NewSoundRecord\\component\\plugin\\plugin_oplus_ns\\src\\main\\cpp\\bridge.cpp", "file": "F:\\project\\NewSoundRecord\\4\\NewSoundRecord\\component\\plugin\\plugin_oplus_ns\\src\\main\\cpp\\bridge.cpp"}]