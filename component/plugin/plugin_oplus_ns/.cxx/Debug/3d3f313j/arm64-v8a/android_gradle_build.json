{"buildFiles": ["F:\\project\\NewSoundRecord\\4\\NewSoundRecord\\component\\plugin\\plugin_oplus_ns\\src\\main\\cpp\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Users\\W9096060\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\project\\NewSoundRecord\\4\\NewSoundRecord\\component\\plugin\\plugin_oplus_ns\\.cxx\\Debug\\3d3f313j\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\Users\\W9096060\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\project\\NewSoundRecord\\4\\NewSoundRecord\\component\\plugin\\plugin_oplus_ns\\.cxx\\Debug\\3d3f313j\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"OplusNS::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "OplusNS", "output": "F:\\project\\NewSoundRecord\\4\\NewSoundRecord\\component\\plugin\\plugin_oplus_ns\\build\\intermediates\\cxx\\Debug\\3d3f313j\\obj\\arm64-v8a\\libOplusNS.so", "runtimeFiles": []}}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\Users\\W9096060\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\Users\\W9096060\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}