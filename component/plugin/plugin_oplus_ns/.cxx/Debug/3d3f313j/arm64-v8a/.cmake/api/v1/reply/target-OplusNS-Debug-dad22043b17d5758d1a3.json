{"artifacts": [{"path": "F:/project/NewSoundRecord/4/NewSoundRecord/component/plugin/plugin_oplus_ns/build/intermediates/cxx/Debug/3d3f313j/obj/arm64-v8a/libOplusNS.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 38, "parent": 0}, {"command": 1, "file": 0, "line": 51, "parent": 0}, {"command": 2, "file": 0, "line": 14, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC"}], "defines": [{"define": "OplusNS_EXPORTS"}], "includes": [{"backtrace": 3, "path": "F:/project/NewSoundRecord/4/NewSoundRecord/component/plugin/plugin_oplus_ns/src/main/cpp/inc"}], "language": "CXX", "sourceIndexes": [0, 2, 5], "sysroot": {"path": "D:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "OplusNS::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"fragment": "-LF:\\project\\NewSoundRecord\\4\\NewSoundRecord\\component\\plugin\\plugin_oplus_ns\\src\\main\\cpp\\..\\jniLibs\\arm64-v8a", "role": "libraryPath"}, {"backtrace": 2, "fragment": "-landroid", "role": "libraries"}, {"fragment": "-lopai_interface", "role": "libraries"}, {"backtrace": 2, "fragment": "-llog", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "D:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "OplusNS", "nameOnDisk": "libOplusNS.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 5]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 4]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "OplusNS.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "OplusNS.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "managed_jnienv.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "managed_jnienv.h", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "LogUtil.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "bridge.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}