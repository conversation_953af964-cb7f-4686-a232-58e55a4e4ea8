{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "OplusNS", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "OplusNS::@6890427a1f51a3e7e1df", "jsonFile": "target-OplusNS-Debug-dad22043b17d5758d1a3.json", "name": "OplusNS", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "F:/project/NewSoundRecord/4/NewSoundRecord/component/plugin/plugin_oplus_ns/.cxx/Debug/3d3f313j/arm64-v8a", "source": "F:/project/NewSoundRecord/4/NewSoundRecord/component/plugin/plugin_oplus_ns/src/main/cpp"}, "version": {"major": 2, "minor": 3}}