apply from: "../../../common_build.gradle"
apply plugin: 'org.jetbrains.kotlin.android'

android {
    namespace 'com.soundrecorder.plugin.ns.cloud'

    android {
        buildFeatures {
            buildConfig = true
        }
    }
}

dependencies {

    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation libs.material
    implementation libs.androidx.constraintlayout
    implementation libs.oplus.coui.dialog
    implementation libs.oplus.coui.progressbar

    implementation project(':component:plugin:plugin_installer_api')
    implementation project(':common:libcommon')
    implementation project(':common:libbase')

    implementation libs.oms.play.core.library
    implementation libs.oms.split.core
    //如果插件需要支持端云下发，还需额外依赖 downloader 模块
    implementation(libs.oms.downloader) {
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }
}