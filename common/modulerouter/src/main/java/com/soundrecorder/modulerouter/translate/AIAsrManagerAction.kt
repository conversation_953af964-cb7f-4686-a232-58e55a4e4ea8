/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: AIAsrManagerAction
 * Description:
 * Version: 1.0
 * Date: 2025/3/26
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/26 1.0 create
 */

package com.soundrecorder.modulerouter.translate

import android.content.Context
import com.soundrecorder.modulerouter.smartname.SmartNameAction.Companion.DETECT_UNIFIED_SUMMARY

interface AIAsrManagerAction {

    fun loadSupportAIAsr(context: Context, forceUpdate: Boolean): Boolean

    fun newAsrPluginDownloadDelegate(): IAsrPluginDownloadDelegate?
    fun newAsrPluginManager(): IAsrPluginCallBack?
    fun isDetectSupported(context: Context, detectName: String = DETECT_UNIFIED_SUMMARY): Boolean?

    fun checkUpdateAIUnitConfigBackground(context: Context)
    fun getSupportLanguage(callback: (Map<String, String>?) -> Unit)
    fun checkUpdate(enableUI: Boolean, callback: (Boolean) -> Unit)
}