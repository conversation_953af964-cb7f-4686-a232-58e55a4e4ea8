/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ISmartNameManager
 * * Description: ISmartNameManager
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: ********
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  ********    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.modulerouter.smartname

import android.app.Activity
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleObserver

interface ISmartNameManager : LifecycleObserver {

    fun convertStartSmartNameClickHandle(
        activity: Activity?,
        selectedMediaIdList: MutableList<Long>?,
        pageFrom: Int?,
        isOpenSwitch: Boolean = false,
        isRealTime: Boolean = false
    )

    fun checkPluginsDownload(activity: Activity?, callback: ((Boolean) -> Unit)? = null, isOpenSwitch: Boolean)

    fun startOrResumeConvertSmartName(
        activity: Activity?,
        selectedMediaIdList: MutableList<Long>?,
        isOpenSwitch: Boolean,
        isRealTime: Boolean
    )

    fun doClickPermissionConvertOK(activity: Activity?, selectedMediaIdList: MutableList<Long>?, pageFrom: Int?) {}

    fun release()

    fun releaseAll()
}