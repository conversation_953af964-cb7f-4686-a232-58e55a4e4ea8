package com.soundrecorder.common.constant

object RecorderConstant {
    /**
     * 录音自己定义的录制format
     */
    const val RECORDER_AUDIO_FORMAT_MP3 = 9
    const val RECORDER_AUDIO_FORMAT_AMR_NB = 3
    const val RECORDER_AUDIO_FORMAT_AMR_WB = 4
    const val RECORDER_AUDIO_FORMAT_WAV = 11
    const val RECORDER_AUDIO_FORMAT_AAC_ADTS = 6

    /*采样率*/
    const val SAMPLE_RATE_16000 = 16000
    const val SAMPLE_RATE_44100 = 44100
    const val SAMPLE_RATE_48000 = 48000

    /**
     * 针对一加录制wav，输出格式及编码
     */
    const val OP_WAV_OUTPUT_FORMAT = 21
    const val OP_WAV_ENCODER = 12
    const val OP_ENCODE_BITRATE_AAC = 96000

    const val MP3_FILE_SUFFIX: String = ".mp3"
    const val AAC_FILE_SUFFIX: String = ".aac"
    const val WAV_FILE_SUFFIX: String = ".wav"
    const val AMR_FILE_SUFFIX: String = ".amr"
    const val AMR_WB_FILE_SUFFIX: String = ".awb"

    // WAV 格式支持录制的最大时长 5小时
    const val MAX_DURATION_WAV_MS = 5 * 60 * 60 * 1000

    const val REALTIME_PCM_QUEUE_CAPACITY = 1500
    const val REALTIME_PCM_QUEUE_TIMEOUT = 40L
}