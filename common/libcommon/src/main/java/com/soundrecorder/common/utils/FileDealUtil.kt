package com.soundrecorder.common.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.app.RecoverableSecurityException
import android.content.ComponentName
import android.content.ContentUris
import android.content.Context
import android.content.Intent
import android.content.IntentSender.SendIntentException
import android.content.pm.PackageManager
import android.database.Cursor
import android.media.MediaScannerConnection
import android.media.MediaScannerConnection.OnScanCompletedListener
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.text.TextUtils
import android.view.View
import androidx.annotation.RequiresApi
import androidx.core.app.ActivityCompat
import androidx.core.content.FileProvider
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.*
import com.soundrecorder.common.R
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA
import com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DISPLAY_NAME
import com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DURATION
import com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID
import com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_ID
import com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SIZE
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.constant.RecorderConstant
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.db.AudioEffectDBUtils
import com.soundrecorder.common.db.CloudSyncRecorderDbUtil
import com.soundrecorder.common.db.ConvertDeleteUtil
import com.soundrecorder.common.db.KeyWordDbUtils.RECORD_ID
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.db.RecycleBinDbUtils.FILE_DIRECTORY_NAME
import com.soundrecorder.common.db.RecycleBinDbUtils.recycleBinUri
import com.soundrecorder.common.flexible.FollowHandDialogUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.share.OShareConvertUtil
import com.soundrecorder.common.utils.RecordModeUtil.getRecordTypeForMediaRecord
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.utils.Injector
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File
import java.nio.file.Files
import java.nio.file.StandardCopyOption
import java.text.SimpleDateFormat
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Date
import java.util.UUID

object FileDealUtil {
    const val TAG: String = "FileDealUtil"
    const val REQUEST_CODE_SHARE = 30001
    private const val SEND_ITEM_LIMIT = 500
    private const val TENCENT_PACKAGE = "com.tencent.mm"
    private const val FILE_NAME_SUFFIX = "_"
    private const val TENCENT_SEND_FRIEND_CLASS = "com.tencent.mm.ui.tools.ShareImgUI"
    private const val HANDLE_ZOMBIE_FILE = "handleZombieFile"
    private const val HANDLE_ZOMBIE_FILE_LIMIT = 10


    private const val SELECTION_DISPLAY_NAME_AND_DATA = " $COLUMN_NAME_DISPLAY_NAME = ? AND $COLUMN_NAME_DATA = ? AND $COLUMN_NAME_SIZE != 0 "
    private val RECYCLE_BIN_ROOT_PATH = Environment.getExternalStorageDirectory().absolutePath + File.separator + FILE_DIRECTORY_NAME
    private val SUB_DIR_STAND_RECORDING_PATH = File.separatorChar + Constants.STANDARD_RECORDINGS
    private val SUB_DIR_MEETING_RECORDING_PATH = File.separatorChar + Constants.MEETING_RECORDINGS
    private val SUB_DIR_INTERVIEW_RECORDING_PATH = File.separatorChar + Constants.INTERVIEW_RECORDINGS
    private val SUB_DIR_CALL_RECORDING_PATH = File.separatorChar + Constants.CALL_RECORDINGS


    private val RECYCLE_BIN_STANDARD_PATH = RECYCLE_BIN_ROOT_PATH + SUB_DIR_STAND_RECORDING_PATH
    private val RECYCLE_BIN_MEETING_PATH = RECYCLE_BIN_ROOT_PATH + SUB_DIR_MEETING_RECORDING_PATH
    private val RECYCLE_BIN_INTERVIEW_PATH = RECYCLE_BIN_ROOT_PATH + SUB_DIR_INTERVIEW_RECORDING_PATH
    private val RECYCLE_BIN_CALL_PATH = RECYCLE_BIN_ROOT_PATH + SUB_DIR_CALL_RECORDING_PATH

    private val standRecordingPath = Constants.RECORDINGS + SUB_DIR_STAND_RECORDING_PATH
    private val meetingRecordingPath = Constants.RECORDINGS + SUB_DIR_MEETING_RECORDING_PATH
    private val interviewRecordingPath = Constants.RECORDINGS + SUB_DIR_INTERVIEW_RECORDING_PATH
    private val callRecordingPath = Constants.RECORDINGS + SUB_DIR_CALL_RECORDING_PATH

    /*标记删除录音记录是否因为删除了录音分组 */
    var mDeleteRecordsByGroupDeleted = false

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    @JvmStatic
    @RequiresApi(Build.VERSION_CODES.R)
    fun delete(mActivity: Activity, uris: List<Uri>?, requestCode: Int? = null) {
        if (uris == null || uris.isEmpty()) {
            DebugUtil.e(TAG, "delete failed ,uris is null or isEmpty")
            return
        }
        val pendingIntent =
            MediaStore.createDeleteRequest(BaseApplication.getAppContext().contentResolver, uris)
        val intentSender = pendingIntent.intentSender
        try {
            ActivityCompat.startIntentSenderForResult(
                mActivity, intentSender, requestCode ?: Constants.REQUEST_CODE_DELETE_BARCH,
                null, 0, 0, 0, null
            )
        } catch (e: SendIntentException) {
            DebugUtil.e(TAG, "delete SendIntentException", e)
        }
    }

    @RequiresApi(Build.VERSION_CODES.R)
    @JvmStatic
    fun recover(mActivity: Activity, uris: List<Uri>?, requestCode: Int? = null) {
        if (uris == null || uris.isEmpty()) {
            DebugUtil.e(TAG, "recover failed ,uris is null or isEmpty")
            return
        }
        val pendingIntent =
            MediaStore.createWriteRequest(BaseApplication.getAppContext().contentResolver, uris)
        val intentSender = pendingIntent.intentSender
        try {
            ActivityCompat.startIntentSenderForResult(
                mActivity, intentSender, requestCode ?: Constants.REQUEST_CODE_DELETE_BARCH,
                null, 0, 0, 0, null
            )
        } catch (e: SendIntentException) {
            DebugUtil.e(TAG, "recover SendIntentException", e)
        }
    }

    @JvmStatic
    fun sendRecordFile(activity: Activity?, id: Long, anchor: View?) {
        if ((id == 0L)) {
            DebugUtil.i(TAG, "onOptionsMenuItemSelected send error, id:$id")
            return
        }
        val uri = ContentUris.withAppendedId(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, id)
        val intent = Intent(Intent.ACTION_SEND)
        val filePath = getRealPathFromURI(activity?.applicationContext, uri)
        if (!filePath.isNullOrEmpty()) {
            var fileUri: Uri? = null
            kotlin.runCatching {
                val file = File(filePath)
                fileUri = getUriForFile(file)
            }
            DebugUtil.i(TAG, "onOptionsMenuItemSelected send: uri=$uri, filePath=$filePath, fileUri=$fileUri")
            if (fileUri == null) {
                return
            }
            val mineType = "audio/*"
            intent.setDataAndType(fileUri, mineType)
            //用于标识互传相关，send_entrance是互传自定义参数
            intent.putExtra(OShareConvertUtil.OPPO_SHARE_SEND_ENTRANCE, activity?.packageName)
            intent.putExtra(Intent.EXTRA_STREAM, fileUri)
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_ACTIVITY_NEW_TASK)
            FollowHandDialogUtils.addShareDialogAnchor(anchor, intent)
            val chooserIntent = Intent.createChooser(intent, BaseApplication.getAppContext().getString(R.string.send))
            activity?.startActivityForResult(chooserIntent, REQUEST_CODE_SHARE)
        }
    }

    /**
     * 分享多个音频文件
     * @return 是否分享成功
     *
    * */
    @JvmStatic
    fun sendRecordFiles(activity: Activity?, checked: ArrayList<String>, anchor: View?): Boolean {
        try {
            if (activity == null) return false
            if (checked.size > SEND_ITEM_LIMIT) {
                ToastManager.showShortToast(BaseApplication.getAppContext(), R.string.send_record_item_exceed_limit)
                return false
            }
            val mineType = "audio/*"
            val recorderFilesUris = ArrayList<Uri>()
            val baseUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
            for (id in checked) {
                val idLong = id.toLong()
                if (idLong == 0L) {
                    continue
                }
                val uri = ContentUris.withAppendedId(baseUri, idLong)
                recorderFilesUris.add(uri)
                DebugUtil.i(TAG, "onOptionsMenuItemSelected send uri:$uri")
            }
            val intent = Intent(Intent.ACTION_SEND)
            //用于标识互传相关，send_entrance是互传自定义参数
            intent.putExtra(OShareConvertUtil.OPPO_SHARE_SEND_ENTRANCE, activity.packageName)
            intent.action = Intent.ACTION_SEND_MULTIPLE
            intent.putParcelableArrayListExtra(Intent.EXTRA_STREAM, recorderFilesUris)
            intent.type = mineType
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            FollowHandDialogUtils.addShareDialogAnchor(anchor, intent)
            startChooserActivity(activity, intent)
            return true
        } catch (e: Throwable) {
            DebugUtil.e(TAG, "sendRecordFiles exception", e)
            return false
        }
    }

    private fun startChooserActivity(activity: Activity?, intent: Intent) {
        val chooserIntent = Intent.createChooser(intent, BaseApplication.getAppContext().getString(R.string.send))
        activity?.startActivityForResult(chooserIntent, REQUEST_CODE_SHARE)
    }

    private fun startChooserActivityWithWeChat(activity: Activity?, intent: Intent) {
        val comp: ComponentName? = getWeChatComp(activity)
        val chooserIntent = Intent.createChooser(intent, BaseApplication.getAppContext().getString(R.string.send))
        if (comp != null) {
            DebugUtil.i(TAG, "onOptionsMenuItemSelected send comp:$comp")
            val intents = arrayOf(Intent(intent).apply {
                component = comp
            })
            chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, intents)
        }
        chooserIntent.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION)
        chooserIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)

        activity?.startActivityForResult(chooserIntent, REQUEST_CODE_SHARE)
    }

    private fun getWeChatComp(activity: Activity?): ComponentName? {
        var comp: ComponentName? = null
        val intent = Intent(Intent.ACTION_SEND)
        intent.setPackage(TENCENT_PACKAGE)
        intent.type = "*/*"

        val sendFriendStr = activity?.resources?.getString(R.string.share_to_wechat_friend)

        val pm: PackageManager? = activity?.packageManager
        val resolveInfos = pm?.queryIntentActivities(intent, PackageManager.GET_RESOLVED_FILTER)
        for (resolveInfo in resolveInfos ?: arrayListOf()) {
            val label = pm?.let { resolveInfo.loadLabel(it).toString() } ?: ""
            if (((resolveInfo.activityInfo?.name
                    ?: "") == TENCENT_SEND_FRIEND_CLASS) || (label.trim() == sendFriendStr)
            ) {
                comp = ComponentName(TENCENT_PACKAGE, resolveInfo.activityInfo.name)
                break
            }
        }

        return comp
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    @Throws(RecoverableSecurityException::class)
    @JvmStatic
    fun delete(uri: Uri): Boolean {
        var deleteResult = -1
        try {
            deleteResult = BaseApplication.getAppContext().contentResolver.delete(uri, null, null)
        } catch (e: Throwable) {
            if (e is RecoverableSecurityException) {
                throw e
            }
            DebugUtil.e(TAG, "delete uri error", e)
        }
        DebugUtil.i(TAG, "delete uri:$uri, deleteResult:$deleteResult, true")
        return deleteResult > 0
    }

    @JvmStatic
    fun delete(path: String): Boolean {
        if (TextUtils.isEmpty(path)) {
            return true
        }
        try {
            val deleteCount: Int = BaseApplication.getAppContext().contentResolver.delete(
                MediaDBUtils.BASE_URI, MediaStore.Audio.Media.DATA + " COLLATE NOCASE =?", arrayOf(path)
            )
            var deleteFileSuc = false
            val file = File(path)
            if (file.exists()) {
                deleteFileSuc = file.delete()
            }
            DebugUtil.d(
                TAG, "deleteCurrentCursor success name = " + FileUtils.getDisplayNameByPath(path) + ", deleteCount: "
                        + deleteCount + " file.exist: " + file.exists() + ", deleteFileSuc: " + deleteFileSuc, true
            )
            return deleteCount > 0
        } catch (e: Exception) {
            DebugUtil.log(TAG, "deleteCurrentCursor, The e is $e")
        }
        return false
    }

    @JvmStatic
    fun deleteRecordDBBatch(records: List<Record>) {
        for (record in records) {
            deleteRecordDB(record)
            ConvertDeleteUtil.deleteConvertData(BaseApplication.getAppContext(), record.getId())
            NoteDbUtils.deleteNoteByMediaId(record.id.toString())
        }
    }

    @JvmStatic
    fun deleteRecordDB(record: Record) {
        if (BaseUtil.isAndroidQOrLater) {
            CloudSyncRecorderDbUtil.deleteRecordDBRecordByPath(
                record.data,
                false
            )
        } else {
            CloudSyncRecorderDbUtil.deleteRecordByPath(record.getData(), false)
        }
    }

    /**
     * 删除音频调用方法
     * @param context
     * @param targetRecord 删除录音文件的信息
     * @param requestCode 请求码，用于activityResult的回调
     * @param isRecycle 是否从回收站删除
     */
    @JvmStatic
    fun deleteRecord(context: Activity, targetRecord: Record, requestCode: Int?, isRecycle: Boolean): Boolean {
        if (isRecycle) {
            //从回收站彻底删除
            return deleteRecycleBinRecordFile(context, targetRecord, requestCode)
        } else {
            //从首页列表删除，文件进入回收站
            return deleteRecord(context, targetRecord, requestCode)
        }
    }


    /**
     * 彻底删除：
     * 1、删除回收站的文件
     * 2、清理回收站数据库、以及对应的其他表的数据
     */
    @JvmStatic
    fun deleteRecycleBinRecordFile(context: Activity, record: Record, requestCode: Int?): Boolean {
        DebugUtil.d(TAG, "deleteRecycleBinRecordFile  record = $record")
        val path = record.data
        val id = record.id
        if (TextUtils.isEmpty(path) && id == -1L) {
            return true
        }
        var deleteResult = false
        val file = File(record.recycleFilePath)

        /*
          2、 数据库数据删除，主要有：
              回收站数据库
              波形文件
              标记
              record表
              转文本
              便签
         */

        // 删除波形文件
        record.deleteAmpFile()
        // 删除图片标记记录、文件
        RecorderDBUtil.deletePictureDbAndFilesByData(record.id.toString())

        //老的流程，删除各个数据库数据的方法
        val deleteSuccess = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).deleteRecordByPathExcludeAudioFile(path)
        if (deleteSuccess && file.exists()) {
            //1、文件删除
            deleteResult = file.delete()
            DebugUtil.d(TAG, "deleteResult = $deleteResult")
        }
        ConvertDeleteUtil.deleteConvertData(context, id)
        NoteDbUtils.deleteNoteByMediaId(id.toString())
        AudioEffectDBUtils.deleteAudioEffectByName(record.displayName)
        return deleteResult
    }


    /**
     * 播放页面删除音频调用方法
     * @param context
     * @param path
     * @param id  媒体库ID
     * @param recordType
     */
    @Throws(Exception::class)
    @JvmStatic
    fun deleteRecord(context: Activity, path: String?, id: Long, requestCode: Int?): Boolean {
        if (TextUtils.isEmpty(path) && id == -1L) {
            return true
        }
        var deleteSuccess = false
        if (BaseUtil.isAndroidQOrLater) {
            deleteSuccess = deleteRecordFromCurrentCursor(context, id, requestCode)
            DebugUtil.i(TAG, "delete file from media id : $id, deleteSuccess: $deleteSuccess, true")
        } else {
            deleteSuccess = deleteCurrentCursor(context, path)
        }
        if (deleteSuccess) {
            deleteRecordDBBatch(context, path, id)
            return true
        }
        return false
    }


    /**
     * 删除音频调用方法
     * @param context
     * @param targetRecord
     * @param mediaId  媒体库ID
     * @param recordType
     */
    @Throws(Exception::class)
    @JvmStatic
    fun deleteRecord(context: Activity, targetRecord: Record, requestCode: Int?): Boolean {
        val path = targetRecord.data
        val id = targetRecord.id
        if (TextUtils.isEmpty(path) && id == -1L) {
            return true
        }
        var deleteRecord = targetRecord
        // 删除前检测下文件是否还存在，因为这儿可能被只能命名给重新命名了，此时文件path路径会对不上,不存在的情况需要通过mediaId重新查询下
        val file = File(path)
        if (!file.exists()) {
            DebugUtil.d(
                TAG,
                "delete record file not exits id = $id, sourceFile=$file query new record info"
            )
            val newRecord = MediaDBUtils.queryRecordById(id)
            if (newRecord != null && !TextUtils.isEmpty(newRecord.data)) {
                DebugUtil.d(TAG, "query new record info newPath=${newRecord.data}")
                deleteRecord = newRecord
            }
        }

        var removeSuccess = false
        //移动到回收站，不执行真正的删除
        removeSuccess = removeRecordToRecycle(context, deleteRecord, id, requestCode)

        AudioEffectDBUtils.deleteAudioEffect(id)

        DebugUtil.d(TAG, "deleteRecord deleteSuccess = $removeSuccess, id = $id, Thread = ${Thread.currentThread()}")
        return removeSuccess
    }


    /**
     * 删除在回收站超过30天的文件
     * @param context
     * @param targetRecord
     * @param mediaId  媒体库ID
     * @param recordType
     */
    @Throws(Exception::class)
    @JvmStatic
    fun deleteOverTimeRecycleRecord(context: Context, deleteRecordList: List<Record>) {
        if (deleteRecordList.isNullOrEmpty()) {
            DebugUtil.d(TAG, "deleteOverTimeRecycleRecord list = null")
            return
        }
        //彻底删除录音文件、record表及波形等数据的 数据库及文件
        deleteRecordList.forEach {
            if (it.recycleFilePath.isNullOrEmpty()) {
                return
            }
            val file = File(it.recycleFilePath)
            if (file.exists()) {
                file.delete()
                RecorderDBUtil.getInstance(BaseApplication.getAppContext()).deleteRecordsById(it.id)
                AudioEffectDBUtils.deleteAudioEffectByPath(it.recycleFilePath)
            }
        }
    }

    @JvmStatic
    fun updateDirtyDataInRecycleBin(dirtyRecordList: List<Record>) {
        if (dirtyRecordList.isNullOrEmpty()) {
            DebugUtil.d(TAG, "updateDirtyDataInRecycleBin list = null")
            return
        }
        DebugUtil.d(TAG, "updateDirtyDataInRecycleBin start")
        dirtyRecordList.forEach {
            updateDirtyRecordInRecycleBin(it)
        }
    }

    /**
     * 脏数据处理
     * 主要是处理的时回收站目录下无文件，但是数据库有记录的脏数据
     */
    @SuppressLint("Recycle")
    @JvmStatic
    private fun updateDirtyRecordInRecycleBin(record: Record) {
        var isUpdateRecordSuccess = false
        try {
            //首先判断目录是否存在
            val targetDirPath = File(record.data).parent
            val targetDirectory = targetDirPath?.let { File(it) }
            if (!targetDirectory?.exists()!!) {
                DebugUtil.d(TAG, "updateDirtyRecordInRecycleBin no dir")
                targetDirectory.mkdirs()
            }
            val originalFile = File(targetDirPath, record.displayName)
            kotlin.runCatching {
                //仅在原目录下有文件的情况才处理数据库记录，不涉及文件移动
                if (originalFile.exists()) {
                    record.data = originalFile.absolutePath //更新绝对路径
                    isUpdateRecordSuccess = CloudSyncRecorderDbUtil.updateRecoveryRecord(BaseApplication.getAppContext(), record)
                    if (isUpdateRecordSuccess) {
                        DebugUtil.d(TAG, "updateDirtyRecordInRecycleBin record = $record")
                    }
                } else {
                    val bResult = CloudSyncRecorderDbUtil.deleteRecordDBRecordByPath(record.data, false)
                    DebugUtil.w(TAG, "updateDirtyRecordInRecycleBin, file not exist, delete dirty record: $bResult")
                }
            }.onFailure {
                DebugUtil.w(TAG, "updateDirtyRecordInRecycleBin failed, error = ${it.message}")
            }
        } catch (e: RecoverableSecurityException) {
            DebugUtil.e(TAG, "catch delete RecoverableSecurityException", e)
        }
        val netWorkGranted = PermissionUtils.isNetWorkGranted(BaseApplication.getAppContext())
        if (netWorkGranted && isUpdateRecordSuccess) {
            //触发云同步
            CloudSyncRecorderDbUtil.trigBackupNow()
        }
    }


    /**
     * 恢复回收站音频数据
     * @param context
     * @param record
     * @param recordType
     */
    @Throws(Exception::class)
    @JvmStatic
    fun recoveryRecord(context: Activity, record: Record, requestCode: Int?): Boolean {
        val path = record.data
        val id = record.id
        if (TextUtils.isEmpty(path) && id == -1L) {
            return true
        }
        var recoverySuccess = false
        //从回收站移动到原有音频目录
        /**
         * 1、自测 是否要判断 有音频权限？
         *
         */
        recoverySuccess = recoveryRecordFromRecycle(context, record, requestCode)
        DebugUtil.d(TAG, "recoveryRecord recoverySuccess = $recoverySuccess")
        return recoverySuccess
    }

    /**
     * 仅在首次启动回收站需求的版本上进行初始化
     * 清空回收站，理论上首次启动带回收站需求的版本时，不会存在回收站目录
     * 应用场景：回收站需求的版本上，删除文件后，清空录音数据
     */
    @JvmStatic
    fun initRecycleFile() {
        CoroutineScope(Dispatchers.IO).launch {
            val recycleDirectory = File(RECYCLE_BIN_ROOT_PATH)
            if (!recycleDirectory.exists() || !recycleDirectory.isDirectory) {
                return@launch
            }
            recycleDirectory.listFiles()?.forEach {
                it.delete()
            }
        }
    }

    /**
     * 根据文件是否有效，未在表里记录的文件则属于僵尸文件，无效
     * @param file 回收站的文件
     * @param recycleBinRecords 回收站记录
     */
    @JvmStatic
    private fun isZombieFile(file: File, recycleBinRecords: List<Record>?): Boolean {
        val recycleBinFileMd5 = MD5Utils.getMD5(file)
        //根据回收站文件路径和md5校验文件是否为僵尸文件
        if (!recycleBinRecords.isNullOrEmpty()) {
            recycleBinRecords.forEach { record ->
                if (record.recycleFilePath == file.absolutePath && record.mD5 == recycleBinFileMd5) {
                    return false
                }
            }
        }
        return true
    }

    /**
     * 处理历史遗留问题产生的未处理文件，将回收站目录下僵尸文件删除
     * 这个方法在DeleteFileDialogUtil deleteWithPermission方法中调用, 已在协程中
     */
    @JvmStatic
    fun handleZombieFilesInRecycleBin() {
        val handleCount = PrefUtil.getInt(BaseApplication.getAppContext(), HANDLE_ZOMBIE_FILE, 1)
        if (handleCount > HANDLE_ZOMBIE_FILE_LIMIT) {
            DebugUtil.d(
                TAG,
                "handleZombieFilesInRecycleBin: handle count limit"
            )
            return
        }
        PrefUtil.putInt(BaseApplication.getAppContext(), HANDLE_ZOMBIE_FILE, handleCount + 1)

        val filesInRecycleBin = getAllFilesFromRecycleBinRootDir()
        if (!filesInRecycleBin.isNullOrEmpty()) {
            val recycleBinRecords =
                RecorderDBUtil.getInstance(BaseApplication.getAppContext()).queryAllRecycleRecords()
            filesInRecycleBin.forEach {
                val isZombieFile = isZombieFile(it, recycleBinRecords)
                if (isZombieFile) {
                    val bDeleted = it.delete()
                    DebugUtil.d(
                        TAG,
                        "handleZombieFilesInRecycleBin delete zombie file = ${it.absolutePath}, isDeleted = $bDeleted"
                    )
                }
            }
        }
    }

    @JvmStatic
    private fun makeDirs(path: String) {
        val directory = File(path)
        if (!directory.exists()) {
            directory.mkdirs()
        }
    }

    @JvmStatic
    fun checkAndCreateRecycleDir(subDir: String?): String {
        var targetPath = RECYCLE_BIN_ROOT_PATH
        when (subDir) {
            Constants.STANDARD_RECORDINGS -> targetPath = RECYCLE_BIN_STANDARD_PATH
            Constants.INTERVIEW_RECORDINGS -> targetPath = RECYCLE_BIN_INTERVIEW_PATH
            Constants.MEETING_RECORDINGS -> targetPath = RECYCLE_BIN_MEETING_PATH
            Constants.CALL_RECORDINGS -> targetPath = RECYCLE_BIN_CALL_PATH
        }
        makeDirs(targetPath)
        return targetPath
    }

    /**
     * 备用， 创建私有目录
     */
    @JvmStatic
    fun checkAndCreatePrivateDir(context: Context, subDir: String?): String {
        var targetPath = context.filesDir.absolutePath
        when (subDir) {
            Constants.STANDARD_RECORDINGS -> targetPath += SUB_DIR_STAND_RECORDING_PATH
            Constants.INTERVIEW_RECORDINGS -> targetPath += SUB_DIR_INTERVIEW_RECORDING_PATH
            Constants.MEETING_RECORDINGS -> targetPath += SUB_DIR_MEETING_RECORDING_PATH
            Constants.CALL_RECORDINGS -> targetPath += SUB_DIR_CALL_RECORDING_PATH
        }
        makeDirs(targetPath)

        return targetPath
    }

    /**
     * 从回收站根目录开始，获取回收站里所有的文件
     */
    @JvmStatic
    fun getAllFilesFromRecycleBinRootDir(): List<File>? {
        val recycleDirPath = checkAndCreateRecycleDir(null)
        return getAllFiles(recycleDirPath)
    }

    /**
     * 从回收站指定的子目录获取所有文件
     */
    @JvmStatic
    fun getAllFilesFromRecycleBinSubDir(subDir: String?): List<File>? {
        val recycleDirPath = checkAndCreateRecycleDir(subDir)
        return getAllFiles(recycleDirPath)
    }

    @JvmStatic
    fun getAllFilesFromPrivateSubDir(context: Context, subDir: String?): List<File>? {
        val dirPath = checkAndCreatePrivateDir(context, subDir)
        return getAllFiles(dirPath)
    }


    @JvmStatic
    private fun getAllFiles(dir: String): List<File> {
        val resultList = ArrayList<File>()
        val directory = File(dir)
        directory.listFiles()?.forEach {
            if (it.isFile) {
                resultList.add(it)
            } else if (it.isDirectory) {
                resultList.addAll(getAllFiles(it.absolutePath))
            }
        }
        return resultList
    }

    /**
     * 根据record不同类型，获取其放置在所对应的不同sub dir 名称
     */
    @JvmStatic
    fun getRecordingsSubDirName(record: Record?): String? {
        if (record == null) {
            return null
        }
        var compareString: String? = null
        compareString = if (BaseUtil.isAndroidQOrLater) {
            record.relativePath
        } else {
            record.data
        }
        if (TextUtils.isEmpty(compareString)) {
            return null
        }
        var outputType: String? = null
        if (compareString.contains(standRecordingPath)) {
            outputType = Constants.STANDARD_RECORDINGS
        } else if (compareString.contains(meetingRecordingPath)) {
            outputType = Constants.MEETING_RECORDINGS
        } else if (compareString.contains(interviewRecordingPath)) {
            outputType = Constants.INTERVIEW_RECORDINGS
        } else if (compareString.contains(callRecordingPath)) {
            outputType = Constants.CALL_RECORDINGS
        }
        DebugUtil.i(
            TAG,
            "getRecordTypeForMediaRecord: compareString: $compareString, type: $outputType"
        )
        return outputType
    }


    @SuppressLint("Recycle")
    @JvmStatic
    private fun removeRecordToRecycle(context: Activity, record: Record, mediaId: Long, requestCode: Int?): Boolean {
        var isMoveSuccess = false
        var cursor: Cursor? = null
        try {
            DebugUtil.d(TAG, "removeRecordToRecycle Record = $record")
            val subDirName = getRecordingsSubDirName(record)
            val subDirFullPathInRecycleBin = checkAndCreateRecycleDir(subDirName)
            //先查询回收站是否有同名文件，如果有，移动以后需要改名字;  其次 需要检查回收站目录下是否有同名文件
            val projections = arrayOf(COLUMN_NAME_ID, COLUMN_NAME_DISPLAY_NAME, COLUMN_NAME_DATA, COLUMN_NAME_DURATION, COLUMN_NAME_GLOBAL_ID)
            val args = arrayOf(record.displayName, record.data)
            val resolver = context.contentResolver
            cursor = resolver.query(DatabaseConstant.RecordUri.RECORD_CONTENT_URI, projections, SELECTION_DISPLAY_NAME_AND_DATA, args, null)
            var newFileName = record.displayName
            //查询回收站目录下是否有同名文件
            var dirHasSameFile = false
            getAllFilesFromRecycleBinSubDir(subDirName)?.forEach {
                if (newFileName.equals(it.name)) {
                    dirHasSameFile = true
                    return@forEach
                }
            }
            val count: Int = cursor?.count ?: -1
            //如果存在同名文件，则对新删除文件进行重命名,加上时间后缀
            if ((cursor != null && count > 1) || dirHasSameFile) {
                val suffix = getNewNameSuffixByConflict(System.currentTimeMillis())
                newFileName = FileUtils.getNewRecycleDisplayName(record.displayName, suffix)
            }
            //标记位，是否从Record表中查询的，首页全部录音是从媒体库查询的，所以id很大
            var hasRecord = false
            var tempRecordId = -1L
            //如果同名文件有多个，需要确定只更新其中一个文件
            while (count >= 1 && cursor?.moveToNext() == true) {
                val id = cursor.getLong(cursor.getColumnIndexOrThrow(COLUMN_NAME_ID))
                val data = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_NAME_DATA))
                val disName = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_NAME_DISPLAY_NAME))
                val duration = cursor.getLong(cursor.getColumnIndexOrThrow(COLUMN_NAME_DURATION))
                val globalId = cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_NAME_GLOBAL_ID))
                DebugUtil.d(TAG, " disName = $disName， duration = $duration， id= $id, data = $data")
                if (record.id == id) {
                    record.globalId = globalId
                    hasRecord = true
                    break
                }
                if (data.contains(record.data) && record.displayName.equals(disName)) {
                    tempRecordId = id
                    record.globalId = globalId
                    break
                }
            }
            DebugUtil.d(TAG, "hasRecord= $hasRecord tempId = $tempRecordId，record.id= ${record.id}, count=$count, cursor=$cursor")
            //检查和传递id是为了保证只更新一条数据
            if (hasRecord) {
                tempRecordId = -1L
            }

            val sourceFile = File(record.data)
            val targetFile = File(subDirFullPathInRecycleBin, newFileName)
            if (sourceFile.exists() && cursor != null && cursor.count == 0) {
                //说明此时没有记录，需要把此录音数据插入到record表
                processInsertData(context, record)
            }

            var updateRecord = false
            //先移动文件，如果成功了再更新数据表，避免因文件管理权限问题，表里先产生了脏数据
            kotlin.runCatching {
                isMoveSuccess = renameToFileWithRetry(sourceFile, targetFile)
                DebugUtil.w(TAG, "removeRecordToRecycle, isMoveSuccess = $isMoveSuccess, sourceFile = $sourceFile, targetFile = $targetFile")
                if (isMoveSuccess) {
                    record.recycleFilePath = targetFile.absolutePath
                    updateRecord = CloudSyncRecorderDbUtil.updateRecordDBRecordFromRecycleBin(
                        record,
                        tempRecordId,
                        newFileName,
                        mDeleteRecordsByGroupDeleted
                    )
                    //如果表更新失败了，文件还原回去
                    if (!updateRecord && targetFile.exists() && !sourceFile.exists()) {
                        val bRecovery = renameToFileWithRetry(targetFile, sourceFile)
                        //再判断一下，隐藏在回收站文件夹的目标文件是否还有，需删除掉
                        if (targetFile.exists()) {
                            targetFile.delete()
                        }
                        if (bRecovery) {
                            //恢复文件后需要触发文件扫描
                            DebugUtil.w(TAG, "removeRecordToRecycle error, updateRecord failed, recovery the srcFile")
                            val paths = arrayOf(sourceFile.absolutePath)
                            val mimeTypes = arrayOf("audio/*")
                            MediaScannerConnection.scanFile(context, paths, mimeTypes, ScanResultListener())
                        }
                    }
                }
            }.onFailure {
                DebugUtil.w(TAG, "removeRecordToRecycle error, ${it.message}")
            }

            if (!isMoveSuccess && targetFile.exists() && targetFile.length() == 0L) {
                //如果文件移动失败，判断是否创建了对应文件，如果有创建且大小为0，则进行删除
                DebugUtil.w(TAG, "removeRecordToRecycle targetFile.length() == 0L, delete.")
                targetFile.delete()
            }
            //移动到回收站，不需要触发云同步，已经在同步数据的位置，判断如果是回收站文件，禁止上传
            val netWorkGranted = cloudKitApi?.isNetWorkGranted(context)
            if (isMoveSuccess && updateRecord && netWorkGranted == true && !TextUtils.isEmpty(record.globalId)) {
                //触发云同步，将云端文件移动到云端回收站
                CloudSyncRecorderDbUtil.trigBackupNow()
            }
            DebugUtil.w(
                TAG,
                "file updateRecord =$updateRecord, isMoveSuccess =$isMoveSuccess,  globalId =${record.globalId}"
            )
        } catch (e: RecoverableSecurityException) {
            DebugUtil.e(TAG, "catch delete RecoverableSecurityException", e)
            try {
                context.startIntentSenderForResult(
                    e.userAction.actionIntent.intentSender,
                    requestCode ?: Constants.REQUEST_CODE_RSE, null, 0, 0, 0
                )
            } catch (se: SendIntentException) {
                DebugUtil.e(TAG, "start intent sender error", se)
            }
        } finally {
            cursor?.close()
        }
        return isMoveSuccess
    }

    /**
     * 相同挂载点，使用renameTo最高效
     * 如果renameTo方法移动失败，则调用moveOrCopyFile进行重试
     */
    @JvmStatic
    private fun renameToFileWithRetry(srcFile: File, targetFile: File): Boolean {
        var isMoveSuccess = false
        kotlin.runCatching {
            if (srcFile.exists()) {
                //相同挂载点，使用renameTo最高效
                isMoveSuccess = srcFile.renameTo(targetFile)
                //如果移动失败，尝试进行复制
                if (!isMoveSuccess) {
                    //如果移动失败
                    isMoveSuccess = moveOrCopyFile(srcFile, targetFile)
                }
            } else {
                DebugUtil.w(
                    TAG,
                    "file not exist, sourceFile = $srcFile, targetFile = $targetFile"
                )
                return false
            }
        }.onFailure {
            DebugUtil.w(TAG, "renameToFileWithRetry sourceFile.renameTo error, ${it.message}")
            if (!isMoveSuccess) {
                //如果移动失败
                isMoveSuccess = moveOrCopyFile(srcFile, targetFile)
            }
        }
        return isMoveSuccess
    }

    /**
     * 如果renameTo方法移动失败，则直接进行移动文件，如果移动失败则复制文件
     */
    @JvmStatic
    private fun moveOrCopyFile(srcFile: File, targetFile: File): Boolean {
        kotlin.runCatching {
            val movePath = Files.move(srcFile.toPath(), targetFile.toPath(), StandardCopyOption.REPLACE_EXISTING)
            if (movePath != null) {
                DebugUtil.d(TAG, "move file path = $movePath")
                return true
            }
        }.onFailure {
            DebugUtil.e(TAG, "move File error: ${it.message}")
        }
        kotlin.runCatching {
            val path = Files.copy(srcFile.toPath(), targetFile.toPath(), StandardCopyOption.REPLACE_EXISTING)
            if (path == null) {
                DebugUtil.d(TAG, "copyFile file failed,  path is null.")
                return false
            }
            targetFile.setLastModified(srcFile.lastModified())
            //删除源文件
            val delete = srcFile.delete()
            //如果此处delete方法删除不掉，是否要使用媒体库的uri来进行删除？
            DebugUtil.d(TAG, "copyFile file path = $path, delete=$delete")
            return true
        }.onFailure {
            DebugUtil.e(TAG, "copy File error: ${it.message}")
        }
        return false
    }

    private class ScanResultListener() : OnScanCompletedListener {

        override fun onScanCompleted(path: String?, uri: Uri?) {
            DebugUtil.d(TAG, "onScanCompleted path=$path, uri=$uri")
        }
    }

    @JvmStatic
    fun processInsertData(context: Context, record: Record?) {
        if (record == null) {
            DebugUtil.d(TAG, "processInSertData record data is empty")
            return
        }
        //var newRecord: Record = record
        var insertUri: Uri? = null
        kotlin.runCatching {
            val uuID = UUID.randomUUID().toString()
            record.uuid = uuID
            record.checkMd5()
            val recordType = getRecordTypeForMediaRecord(record)
            if (TextUtils.isEmpty(record.relativePath)) {
                val relativePath = RecorderDBUtil.getRelativePathForData(record.data, record.displayName)
                record.relativePath = relativePath
            }
            record.recordType = recordType
            record.syncType = RecordConstant.SYNC_TYPE_BACKUP
            record.setSyncUploadStatus(RecordConstant.SYNC_STATUS_BACKUP_START)
            record.dirty = RecordConstant.RECORD_DIRTY_FILE_AND_MEGA
            insertUri = context.contentResolver.insert(
                DatabaseConstant.RecordUri.RECORD_CONTENT_URI,
                record.convertToContentValues()
            )
            if (insertUri != null) {
                val id: Long = ContentUris.parseId(insertUri!!)
                if (id > 0L) {
                    record.id = id
                }
            }
        }.onFailure {
            DebugUtil.e(TAG, "processInSertData, error = ${it.message}")
        }
        DebugUtil.d(TAG, "processInsertData, insertUri = $insertUri, id = ${record.id}")
    }


    @SuppressLint("Recycle")
    @JvmStatic
    private fun recoveryRecordFromRecycle(context: Activity, record: Record, requestCode: Int?): Boolean {
        var isMoveSuccess = false
        var isUpdateRecordSuccess = false
        try {
            //首先判断目录是否存在
            val targetDirPath = File(record.data).parent
            if (targetDirPath.isNullOrEmpty()) {
                DebugUtil.e(TAG, "recoveryRecord targetDirPath is null, record:$record")
                return true
            }
            val targetDirectory = File(targetDirPath)
            if (!targetDirectory.exists()) {
                DebugUtil.d(TAG, "recoveryRecordFromRecycle no dir")
                targetDirectory.mkdirs()
            }
            //查询媒体库对应类型的录音目录下是否有同名文件，如果有，移动以后需要改名字
            val sameFileId = MediaDBUtils.queryRowIdByRelativePathAndDisplayName(record.relativePath, record.displayName)
            //如果存在同名文件，则对新删除文件进行重命名
            if (sameFileId > 0) {
                val suffix = getNewNameSuffixByConflict(System.currentTimeMillis())
                record.displayName = FileUtils.getNewRecycleDisplayName(record.displayName, suffix)
            }
            DebugUtil.d(TAG, "sameFileId: $sameFileId, dis: ${record.displayName}, targetDirPath:$targetDirPath, record:$record")
            if (record.recycleFilePath.isNullOrEmpty()) {
                DebugUtil.e(TAG, "recoveryRecord recycleFilePath is null, record:$record")
                return true
            }
            val sourceFile = File(record.recycleFilePath)
            if (record.displayName.isNullOrBlank()) {
                DebugUtil.e(TAG, "recoveryRecord displayName is null, record:$record")
                return true
            }
            val targetFile = File(targetDirPath, record.displayName)
            kotlin.runCatching {
                if (sourceFile.exists()) {
                    isMoveSuccess = renameToFileWithRetry(sourceFile, targetFile)
                    DebugUtil.w(TAG, "recoveryRecordFromRecycle isMoveSuccess = $isMoveSuccess")
                } else {
                    val recycleBinRecord = RecorderDBUtil.getInstance(context)
                        .qureyRecycleRecordByPath(sourceFile.absolutePath)
                    if (recycleBinRecord != null && recycleBinRecord.isRecycle) {
                        RecorderDBUtil.getInstance(context).deleteRecordsById(recycleBinRecord.id)
                    }
                    DebugUtil.w(TAG, "recoveryRecordFromRecycle failed, sourceFile not exist, sourceFile = $sourceFile,targetFile = $targetFile")
                }
                AudioEffectDBUtils.deleteAudioEffectByPath(record.recycleFilePath)
            }.onFailure {
                DebugUtil.w(TAG, "recoveryRecordFromRecycle sourceFile.renameTo failed, error = ${it.message}")
            }
            if (!isMoveSuccess && targetFile.exists() && targetFile.length() == 0L) {
                DebugUtil.w(TAG, "recoveryRecordFromRecycle targetFile.length() == 0L, delete.")
                //如果文件移动失败，判断是否创建了对应文件，如果有创建且大小为0，则进行删除
                targetFile.delete()
            }
            if (isMoveSuccess) {
                //移动成功，需要对record表进行更新, 并上传元数据
                record.data = targetFile.absolutePath //更新绝对路径
                isUpdateRecordSuccess = CloudSyncRecorderDbUtil.updateRecoveryRecord(context, record)

                //如果删除是是通过复制文件完成的，则恢复文件时需要触发文件扫描
                val paths = arrayOf(targetFile.absolutePath)
                val mimeTypes = arrayOf("audio/*")
                MediaScannerConnection.scanFile(context, paths, mimeTypes, ScanResultListener())
            }
        } catch (e: RecoverableSecurityException) {
            DebugUtil.e(TAG, "catch delete RecoverableSecurityException", e)
            try {
                context.startIntentSenderForResult(
                    e.userAction.actionIntent.intentSender,
                    requestCode ?: Constants.REQUEST_CODE_RSE, null, 0, 0, 0
                )
            } catch (se: SendIntentException) {
                DebugUtil.e(TAG, "start intent sender error", se)
            }
        }
        val netWorkGranted = cloudKitApi?.isNetWorkGranted(context) ?: false
        if (netWorkGranted && isMoveSuccess && isUpdateRecordSuccess) {
            //恢复数据，云同步及本地如何处理？  触发云同步，将云端文件移动到云端回收站
            CloudSyncRecorderDbUtil.trigBackupNow()
        }
        DebugUtil.d(TAG, "netWorkGranted: $netWorkGranted, isMoveSuccess: $isMoveSuccess, isUpdateRecordSuccess:$isUpdateRecordSuccess")
        return isMoveSuccess && isUpdateRecordSuccess
    }

    @SuppressLint("SimpleDateFormat")
    @JvmStatic
    fun getNewNameSuffixByConflict(dateTimeMills: Long?): String {
        if (dateTimeMills == null || dateTimeMills <= 0) {
            return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
        }
        return SimpleDateFormat("yyyyMMddHHmmss").format(Date(dateTimeMills))
    }

    /**
     * 删除录音自己数据库对应的记录内容
     */
    @JvmStatic
    fun deleteRecordDBBatch(context: Activity, path: String?, id: Long): Boolean {
        val deleteResult = if (BaseUtil.isAndroidQOrLater) {
            CloudSyncRecorderDbUtil.deleteRecordDBRecordByPath(
                path, false
            )
        } else {
            CloudSyncRecorderDbUtil.deleteRecordByPath(path, false)
        }
        ConvertDeleteUtil.deleteConvertData(context, id)
        NoteDbUtils.deleteNoteByMediaId(id.toString())
        return deleteResult
    }

    /**
     * 删除回收站数据库对应的记录内容
     */
    @JvmStatic
    fun deleteRecycleBinDBBatch(context: Activity, path: String?, id: Long): Boolean {
        val where = " $RECORD_ID = ? "
        val args = arrayOf(id.toString())
        val delete = context.contentResolver.delete(recycleBinUri, where, args)
        return delete > 0
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    @JvmStatic
    private fun deleteRecordFromCurrentCursor(context: Activity, rowId: Long, requestCode: Int?): Boolean {
        var isDeleteSuccess = false
        val deleteUri = MediaDBUtils.genUri(rowId)
        try {
            isDeleteSuccess = MediaDBUtils.delete(deleteUri) > 0
        } catch (e: RecoverableSecurityException) {
            DebugUtil.e(TAG, "catch delete RecoverableSecurityException", e)
            try {
                context.startIntentSenderForResult(
                    e.userAction.actionIntent.intentSender,
                    requestCode ?: Constants.REQUEST_CODE_RSE, null, 0, 0, 0
                )
            } catch (se: SendIntentException) {
                DebugUtil.e(TAG, "start intent sender error", se)
            }
        }
        return isDeleteSuccess
    }

    private fun deleteCurrentCursor(context: Context, path: String?): Boolean {
        if (TextUtils.isEmpty(path)) {
            return true
        }
        try {
            val file = File(path)
            if (file.exists()) {
                if (file.delete()) {
                    val deleteCount = context.contentResolver.delete(MediaDBUtils.BASE_URI, MediaStore.Audio.Media.DATA + "=?", arrayOf(path))
                    DebugUtil.d(
                        TAG,
                        "deleteCurrentCursor success name = " + FileUtils.getDisplayNameByPath(path) + ", deleteCount: " + deleteCount,
                        true
                    )
                    return deleteCount > 0
                }
            } else {
                val deleteCount = context.contentResolver.delete(MediaDBUtils.BASE_URI, MediaStore.Audio.Media.DATA + "=?", arrayOf(path))
                DebugUtil.d(
                    TAG, "file not exist just delete the record deleteCurrentCursor success name = " + FileUtils.getDisplayNameByPath(path) +
                            ", deleteCount: " + deleteCount,
                    true
                )
                return deleteCount > 0
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "deleteCurrentCursor, The e is $e")
        }
        return false
    }

    @JvmStatic
    fun renameAgain(record: Record?, newName: String?): Boolean {
        if (record == null) {
            return false
        }
        val uri = MediaDBUtils.genUri(record.id)
        val displayName = record.displayName
        var suffix = ""
        if (!TextUtils.isEmpty(displayName) && displayName.contains(".")) {
            suffix = displayName.substring(displayName.lastIndexOf("."))
        }
        return renameAgain(uri, newName, suffix)
    }

    @JvmStatic
    fun renameAgain(uri: Uri?, newName: String?, suffix: String?): Boolean {
        var result = -1
        val dbRecord = RecorderDBUtil.getInstance(BaseApplication.getAppContext())
            .getRecordFromDBbyMediaUri(uri)
        var mimeType: String? = null
        if (dbRecord != null) {
            mimeType = dbRecord.mimeType
        }
        if (uri != null && !TextUtils.isEmpty(newName) && !TextUtils.isEmpty(suffix)) {
            try {
                result = MediaDBUtils.rename(uri, newName, suffix, mimeType)
            } catch (e: Throwable) {
                DebugUtil.e(TAG, "rename", e)
            }
        }
        if (dbRecord != null) {
            val originalName = dbRecord.displayName
            val relativePath = dbRecord.relativePath
            val updateSuc = CloudSyncRecorderDbUtil.updateDisplayName(
                originalName,
                newName + suffix,
                relativePath,
                true,
                dbRecord.originalName,
                RecordConstant.RENAME_TYPE_MANUALLY
            )
            DebugUtil.i(
                TAG,
                "update db name, originalName: " + originalName + ", relativePath: " + relativePath +
                        ", new Name: " + (newName + suffix) + ", updateSuc: " + updateSuc
            )
        }
        return result > 0
    }

    @JvmStatic
    fun getUriForFile(file: File): Uri? {
        return runCatching {
            FileProvider.getUriForFile(
                BaseApplication.getAppContext(), "${DatabaseConstant.PACKAGE_NAME}.fileProvider", file
            )
        }.onFailure {
            DebugUtil.e(TAG, "getUriForFile", it)
        }.getOrNull()
    }

    @JvmStatic
    fun getRealPathFromURI(context: Context?, contentUri: Uri): String? {
        var cursor: Cursor? = null
        return try {
            val proj = arrayOf(MediaStore.Audio.Media.DATA)
            cursor = context?.contentResolver?.query(contentUri, proj, null, null, null)
            if (cursor != null && cursor.count > 0) {
                cursor.moveToNext()
                val columnIndex = cursor.getColumnIndex(proj[0])
                cursor.getString(columnIndex)
            } else {
                null
            }
        } finally {
            cursor?.close()
        }
    }

    /**
     * 检查文件名后缀与mime type是否匹配
     * @param displayName 当前带后缀的文件名
     * @param mimeType 文件真实的mime type
     */
    @JvmStatic
    fun isDisplayNameExtMatchWithMimeType(displayName: String?, mimeType: String?): Boolean {
        if (displayName.isNullOrEmpty() || mimeType.isNullOrEmpty()) {
            DebugUtil.d(TAG, "isDisplayNameExtMatchWithMimeType: displayName or mimeType is null")
            return true
        }
        val lastIndex = displayName.lastIndexOf(".")
        if (lastIndex > 0) {
            val ext = displayName.substring(lastIndex)
            if (getSuffixByMimeType(mimeType).equals(ext, true)) {
                return true
            }
        }
        return false
    }

    @JvmStatic
    fun getSuffixByMimeType(mimeType: String?): String {
        val result = when (mimeType) {
            RecordConstant.MIMETYPE_MP3 -> RecorderConstant.MP3_FILE_SUFFIX
            RecordConstant.MIMETYPE_WAV -> RecorderConstant.WAV_FILE_SUFFIX
            RecordConstant.MIMETYPE_ACC, RecordConstant.MIMETYPE_ACC_ADTS -> RecorderConstant.AAC_FILE_SUFFIX
            RecordConstant.MIMETYPE_AMR -> RecorderConstant.AMR_FILE_SUFFIX
            RecordConstant.MIMETYPE_AMR_WB -> RecorderConstant.AMR_WB_FILE_SUFFIX
            else -> RecorderConstant.MP3_FILE_SUFFIX
        }
        return result
    }
}