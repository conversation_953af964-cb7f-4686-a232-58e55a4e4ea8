<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.coui.appcompat.preference.COUIPreferenceCategory>
        <com.coui.appcompat.preference.COUIJumpPreference
            android:layout="@layout/share_link_top_panel"
            android:selectable="false"
            app:couiShowDivider="false" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>
    <com.coui.appcompat.preference.COUIPreferenceCategory>
        <com.coui.appcompat.preference.COUICheckBoxPreference
            android:defaultValue="true"
            android:key="share_link_audio"
            android:persistent="false"
            android:title="@string/playback_audio"
            app:couiIsCustomIcon="true"
            app:couiShowDivider="true"
            app:coui_jump_mark="@null" />
        <com.coui.appcompat.preference.COUICheckBoxPreference
            android:defaultValue="false"
            android:key="share_link_text"
            android:persistent="false"
            android:title="@string/original_text"
            app:couiIsCustomIcon="true"
            app:couiShowDivider="true" />
        <com.coui.appcompat.preference.COUICheckBoxPreference
            android:defaultValue="false"
            android:key="share_link_summary"
            android:persistent="false"
            android:title="@string/summary"
            app:couiIsCustomIcon="true"
            app:couiShowDivider="true" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>
    <com.coui.appcompat.preference.COUIPreferenceCategory>
        <com.soundrecorder.common.flexible.ShareSummaryAndTextPreference
            android:key="share_summary_dialog_button"
            android:selectable="false"
            android:layout="@layout/share_sunmmary_button_panel" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>
</PreferenceScreen>