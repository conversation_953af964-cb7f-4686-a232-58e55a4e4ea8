package com.soundrecorder.common.db;

import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_AMP_DATA;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_AMP_FILE_PATH;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_BUCKET_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_CREATED;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_MODIFIED;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DISPLAY_NAME;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DURATION;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_ID;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MARK_DATA;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MD5;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MIGRATE_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_MIMETYPE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_PRIVATE_STATUS;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_RECORD_TYPE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_RELATIVE_PATH;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_SIZE;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_NORMAL;
import static com.soundrecorder.common.constant.RecordModeConstant.STORAGE_RECORD_ABOVE_Q;
import static com.soundrecorder.common.utils.MarkSerializUtil.VERSION_NEW;

import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.net.Uri;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.MD5Utils;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.constant.RecordConstant;
import com.soundrecorder.common.constant.RecordModeConstant;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.databean.SaveRecordInfo;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.shadows.ShadowBaseUtils;
import com.soundrecorder.common.shadows.ShadowFeatureOption;
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowBaseUtils.class, ShadowFeatureOption.class})
public class RecorderDbUtilTest {
    private static final String GET_RELEATIVEPATH_FROM_RECORDER_TYPE = "getRelativePathFromRecorderType";
    private static final String GET_WHERE_FROM_RECORDER_TYPE_ABOVE_Q = "getWhereFromRecorderTypeAboveQ";
    private static final String[] sSupportMimeType = new String[]{RecordConstant.MIMETYPE_MP3, RecordConstant.MIMETYPE_AMR, RecordConstant.MIMETYPE_AMR_WB};
    private static final String MP3_PATH_TEXT = "emulated/0/Music/Recordings/Standard Recordings/1.mp3";

    private Context mContext;
    private RecorderDBUtil mRecorderDbUtil;
    private MockedStatic<BaseApplication> mMockBaseApplication;

    @Before
    public void setUp() {
        mContext = Mockito.spy(ApplicationProvider.getApplicationContext());
        mRecorderDbUtil = RecorderDBUtil.getInstance(mContext);

        ContentResolver mockContentResolver = Mockito.mock(ContentResolver.class);
        Mockito.when(mContext.getContentResolver()).thenReturn(mockContentResolver);

        mMockBaseApplication = Mockito.mockStatic(BaseApplication.class);
        mMockBaseApplication.when(BaseApplication::getAppContext).thenReturn(mContext);
        mMockBaseApplication.when(() -> mContext.getContentResolver()).thenReturn(mockContentResolver);
        mMockBaseApplication.when(() -> BaseApplication.getAppContext().getContentResolver()).thenReturn(mockContentResolver);
    }

    @After
    public void teardown() {
        if (mMockBaseApplication != null) {
            mMockBaseApplication.close();
            mMockBaseApplication = null;
        }
        mContext = null;
    }

    private ContentValues genValues() {
        ContentValues values = new ContentValues();
        values.put(COLUMN_NAME_UUID, UUID.randomUUID().toString());
        values.put(COLUMN_NAME_DATA, "emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3");
        values.put(COLUMN_NAME_SIZE, 512);
        values.put(COLUMN_NAME_DISPLAY_NAME, "123.mp3");
        values.put(COLUMN_NAME_MIMETYPE, "audio/mpeg");
        values.put(COLUMN_NAME_DATE_CREATED, System.currentTimeMillis());
        values.put(COLUMN_NAME_DATE_MODIFIED, System.currentTimeMillis());
        values.put(COLUMN_NAME_RECORD_TYPE, 0);
        values.put(COLUMN_NAME_MARK_DATA, "");
        values.put(COLUMN_NAME_AMP_DATA, "");
        values.put(COLUMN_NAME_DURATION, 12000);
        values.put(COLUMN_NAME_BUCKET_ID, "");
        values.put(COLUMN_NAME_DIRTY, 0);
        values.put(COLUMN_NAME_DELETE, RECORD_NORMAL);
        values.put(COLUMN_NAME_RELATIVE_PATH, "\\Music\\Recordings\\Standard Recordings\\");
        values.put(COLUMN_NAME_AMP_FILE_PATH, "");
        values.put(COLUMN_NAME_GLOBAL_ID, "abcdefg");
        values.put(COLUMN_NAME_PRIVATE_STATUS, 0);
        values.put(COLUMN_NAME_MIGRATE_STATUS, 0);
        return values;
    }

    @Test
    public void should_updateRecordMarkById() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            long id = ContentUris.parseId(newUri);
            mRecorderDbUtil.updateRecordMarkById(id, "123");
            String selection = COLUMN_NAME_ID + " = " + id;
            List<Record> updateRecord = RecorderDBUtil.getRecordData(mContext, null, selection, null, null);
            if (updateRecord != null && updateRecord.size() > 0) {
                Record record = updateRecord.get(0);
                Assert.assertNotNull(record.getMarkData());
            }
        }
    }

    @Test
    public void should_deleteRecordData() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            long id = ContentUris.parseId(newUri);
            String selection = COLUMN_NAME_ID + " = " + id;
            RecorderDBUtil.deleteRecordData(mContext, selection, null);
            List<Record> updateRecord = RecorderDBUtil.getRecordData(mContext, null, selection, null, null);
            boolean deleteSucc = (updateRecord == null || updateRecord.size() == 0);
            Assert.assertTrue(deleteSucc);
        }
        Uri newUri1 = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri1 != null) {
            boolean result = mRecorderDbUtil.deleteRecordByPathExcludeAudioFile("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3");
            Assert.assertTrue(result);
        }
    }

    @Test
    public void should_updateRecordAmplitudeById() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            long id = ContentUris.parseId(newUri);
            mRecorderDbUtil.updateRecordAmplitudeById(id, "123");
            String selection = COLUMN_NAME_ID + " = " + id;
            List<Record> updateRecord = RecorderDBUtil.getRecordData(mContext, null, selection, null, null);
            if (updateRecord != null && updateRecord.size() > 0) {
                Record record = updateRecord.get(0);
                Assert.assertNotNull(record.getAmpData());
            }
        }
    }

    @Test
    public void should_getRecordByRelativePathAndDisplayName() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            Record record = mRecorderDbUtil.getRecordByRelativePathAndDisplayName("\\Music\\Recordings\\Standard Recordings\\", "123.mp3");
            Assert.assertNotNull(record);
        }
    }

    @Test
    public void should_deleteRecordByRelativePathAndDisplayName() {
        ContentValues contentValues = genValues();
        Uri newUriOne = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUriOne != null) {
            boolean nullPath = CloudSyncRecorderDbUtil.deleteRecordDBRecordByPath("",  false);
            Assert.assertTrue(nullPath);
        }
        Uri newUriTwo = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUriTwo != null) {
            boolean notFromClound = CloudSyncRecorderDbUtil.deleteRecordDBRecordByPath("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3", false);
            Assert.assertFalse(notFromClound);
        }
    }

    @Test
    public void should_queryRecordByPath() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            Record nullPathrecord = mRecorderDbUtil.qureyRecordByPath("");
            Assert.assertNull(nullPathrecord);
            Record record = mRecorderDbUtil.qureyRecordByPath("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3");
            Assert.assertNotNull(record);
        }
    }

    @Test
    public void should_qureyAllVisibleRecordsCount() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            long count = mRecorderDbUtil.qureyAllVisibleRecordsCount();
            Assert.assertTrue(count > 0);
        }
    }

    @Test
    public void should_updateRealFileSizeOrMd5() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            Record record = mRecorderDbUtil.qureyRecordByPath("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3");
            Assert.assertNotNull(record);
            boolean updateResult = mRecorderDbUtil.updateRealFileSizeOrMd5(record, 512, "");
            Assert.assertTrue(updateResult);
        }
    }

    @Test
    public void should_updateRecordData() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            long id = ContentUris.parseId(newUri);
            String selection = COLUMN_NAME_ID + " = " + id;
            ContentValues newContentValues = new ContentValues();
            newContentValues.put(COLUMN_NAME_DISPLAY_NAME, "456.mp3");
            newContentValues.put(COLUMN_NAME_DATA, "emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3");
            RecorderDBUtil.updateRecordData(mContext, newContentValues, selection, null);
            Record record = mRecorderDbUtil.qureyRecordByPath("emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3");
            Assert.assertNotNull(record);
            Assert.assertEquals("456.mp3", record.getDisplayName());
        }
    }


    @Test
    public void should_getRecordByGlobalId_return_null_when_globalId_is_null() {
        Record record = mRecorderDbUtil.getRecordByGlobalId(null);
        Assert.assertNull(record);
    }

    @Test
    public void should_getRecordByGlobalId_return_notnull_when_globalid_is_valid() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            Record record = mRecorderDbUtil.getRecordByGlobalId("abcdefg");
            Assert.assertNotNull(record);
        }
    }

    @Test
    public void shouldNotNull_when_getRelativePathFromRecorderType() throws Exception {
        String standard = Whitebox.invokeMethod(mRecorderDbUtil, GET_RELEATIVEPATH_FROM_RECORDER_TYPE, RecordModeConstant.RECORD_TYPE_STANDARD);
        String conference = Whitebox.invokeMethod(mRecorderDbUtil, GET_RELEATIVEPATH_FROM_RECORDER_TYPE, RecordModeConstant.RECORD_TYPE_CONFERENCE);
        String interview = Whitebox.invokeMethod(mRecorderDbUtil, GET_RELEATIVEPATH_FROM_RECORDER_TYPE, RecordModeConstant.RECORD_TYPE_INTERVIEW);
        String call = Whitebox.invokeMethod(mRecorderDbUtil, GET_RELEATIVEPATH_FROM_RECORDER_TYPE, RecordModeConstant.RECORD_TYPE_CALL);
        String other = Whitebox.invokeMethod(mRecorderDbUtil, GET_RELEATIVEPATH_FROM_RECORDER_TYPE, RecordModeConstant.RECORD_TYPE_OTHER);

        Assert.assertNotNull(standard);
        Assert.assertNotNull(conference);
        Assert.assertNotNull(interview);
        Assert.assertNotNull(call);
        Assert.assertNotNull(other);

        Assert.assertTrue(standard.contains(Constants.STANDARD_RECORDINGS));
        Assert.assertTrue(conference.contains(Constants.MEETING_RECORDINGS));
        Assert.assertTrue(interview.contains(Constants.INTERVIEW_RECORDINGS));
        Assert.assertTrue(call.contains(Constants.CALL_RECORDINGS));
        Assert.assertTrue(other.contains(STORAGE_RECORD_ABOVE_Q));

    }

    @Test
    public void shouldNotNull_when_getWhereFromRecorderTypeAboveQ() throws Exception {
        String standard = Whitebox.invokeMethod(mRecorderDbUtil, GET_WHERE_FROM_RECORDER_TYPE_ABOVE_Q, RecordModeConstant.RECORD_TYPE_STANDARD, sSupportMimeType);
        String conference = Whitebox.invokeMethod(mRecorderDbUtil, GET_WHERE_FROM_RECORDER_TYPE_ABOVE_Q, RecordModeConstant.RECORD_TYPE_CONFERENCE, sSupportMimeType);
        String interview = Whitebox.invokeMethod(mRecorderDbUtil, GET_WHERE_FROM_RECORDER_TYPE_ABOVE_Q, RecordModeConstant.RECORD_TYPE_INTERVIEW, sSupportMimeType);
        String call = Whitebox.invokeMethod(mRecorderDbUtil, GET_WHERE_FROM_RECORDER_TYPE_ABOVE_Q, RecordModeConstant.RECORD_TYPE_CALL, sSupportMimeType);
        String other = Whitebox.invokeMethod(mRecorderDbUtil, GET_WHERE_FROM_RECORDER_TYPE_ABOVE_Q, RecordModeConstant.RECORD_TYPE_OTHER, sSupportMimeType);

        Assert.assertNotNull(standard);
        Assert.assertNotNull(conference);
        Assert.assertNotNull(interview);
        Assert.assertNotNull(call);
        Assert.assertNotNull(other);

        Assert.assertTrue(standard.contains(Constants.STANDARD_RECORDINGS));
        Assert.assertTrue(conference.contains(Constants.MEETING_RECORDINGS));
        Assert.assertTrue(interview.contains(Constants.INTERVIEW_RECORDINGS));
        Assert.assertTrue(call.contains(Constants.CALL_RECORDINGS));
        Assert.assertTrue(other.contains(STORAGE_RECORD_ABOVE_Q));

    }

    @Test
    public void should_returnFalse_when_insertOrUpdateNewRecord() throws Exception {
        ContentValues contentValues = genValues();
        List<MarkDataBean> markDataBeans = new ArrayList<>();
        markDataBeans.add(new MarkDataBean(12L, VERSION_NEW));
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            SaveRecordInfo saveRecordInfo = new SaveRecordInfo();
            saveRecordInfo.setMarkList(markDataBeans);
            boolean insertResult = mRecorderDbUtil.insertOrUpdateNewRecord(new File("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3"),
                    saveRecordInfo);
            Assert.assertFalse(insertResult);
        }

        boolean insertResult = mRecorderDbUtil.insertOrUpdateNewRecord((File) null, null);
        Assert.assertFalse(insertResult);

        insertResult = Whitebox.invokeMethod(mRecorderDbUtil, "insertOrUpdateNewRecord", (Record)null);
        Assert.assertFalse(insertResult);
    }

    @Test
    public void should_returnFalse_when_insertOrUpdateNewRecord3() {
        ContentValues contentValues = genValues();
        List<MarkDataBean> markDataBeans = new ArrayList<>();
        markDataBeans.add(new MarkDataBean(12L, VERSION_NEW));
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            SaveRecordInfo saveRecordInfo = new SaveRecordInfo();
            saveRecordInfo.setMarkList(markDataBeans);
            saveRecordInfo.setRecordType(RecordModeConstant.RECORD_TYPE_CALL);
            boolean insertResult = mRecorderDbUtil.insertOrUpdateNewRecord(newUri, true, saveRecordInfo);
            Assert.assertTrue(insertResult);
        }
    }

    @Test
    public void should_returnFalse_when_insertOrUpdateCallRecord() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            boolean insertResult = mRecorderDbUtil.insertOrUpdateCallRecord(
                    newUri, "456.mp3");
            Assert.assertTrue(insertResult);
        }
    }

    @Test
    public void should_returnFalse_when_insertOrUpdateNewRecord2() throws Exception {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            long id = ContentUris.parseId(newUri);
            String selection = COLUMN_NAME_ID + " = " + id;
            ContentValues newContentValues = new ContentValues();
            newContentValues.put(COLUMN_NAME_DISPLAY_NAME, "456.mp3");
            newContentValues.put(COLUMN_NAME_MD5, MD5Utils.getMD5(new File("emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3")));
            newContentValues.put(COLUMN_NAME_GLOBAL_ID, "abcdefg");
            newContentValues.put(COLUMN_NAME_DATA, "emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3");
            RecorderDBUtil.updateRecordData(mContext, newContentValues, selection, null);
            Record record = mRecorderDbUtil.qureyRecordByPath("emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3");
            boolean insertResult = Whitebox.invokeMethod(mRecorderDbUtil, "insertOrUpdateNewRecord", record);
            Assert.assertTrue(insertResult);
        }
    }

    @Test
    public void should_returnFalse_when_updateDisplayName() {
        boolean isupdata = CloudSyncRecorderDbUtil.updateDisplayName("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3",
                "emulated\\0\\Music\\Recordings\\Call Recordings\\123.mp3", true, true);
        Assert.assertFalse(isupdata);
    }

    @Test
    public void should_returnFalse_when_updateDisplayNameForDownloadRename() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            long id = ContentUris.parseId(newUri);
            boolean isupdata = CloudSyncRecorderDbUtil.updateDisplayNameForDownloadRename(id, "emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3", true);
            Assert.assertTrue(isupdata);
        }
    }

    @Test
    public void should_returnFalse_when_updateDisplayNameForRecoveryRename() {
        Record record = new Record();
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            boolean isupdata = CloudSyncRecorderDbUtil.updateDisplayNameForRecoveryRename(true,"abcdefg", record);
            Assert.assertTrue(isupdata);
        }
    }

    @Test
    public void should_returnFalse_when_updateRecordAmplitudeByPath() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            boolean isupdata = mRecorderDbUtil.updateRecordAmplitudeByPath("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3",
                    "emulated\\0\\Music\\Recordings\\Call Recordings\\123.mp3");
            Assert.assertTrue(isupdata);
        }
    }

    @Test
    public void should_returnFalse_when_updateRecordIsMarkListShowingByPath() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            boolean isupdata = mRecorderDbUtil.updateRecordIsMarkListShowingByPath("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3", 2);
            Assert.assertFalse(isupdata);
        }
    }

    @Test
    public void should_returnFalse_when_updateRecordMarkByPath() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            boolean isupdata = mRecorderDbUtil.updateRecordMarkByPath("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3",
                    "emulated\\0\\Music\\Recordings\\Call Recordings\\123.mp3");
            Assert.assertTrue(isupdata);
        }
    }

    @Test
    public void should_returnFalse_when_deleteRecordByPath() throws Exception {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            boolean deleteSuccess = mRecorderDbUtil.deleteRecordByPath("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3");
            Assert.assertTrue(deleteSuccess);
        }
    }

    @Test
    public void should_returnFalse_when_directDeleteFileAndRecord() throws Exception {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            boolean isupdata = Whitebox.invokeMethod(mRecorderDbUtil, "directDeleteFileAndRecord", "emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3");
            Assert.assertTrue(isupdata);
        }
    }

    @Test
    public void should_returnFalse_when_markRecordAsDeleted() throws Exception {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            boolean isupdata = Whitebox.invokeMethod(mRecorderDbUtil, "markRecordAsDeleted", "emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3");
            Assert.assertTrue(isupdata);
        }
    }

    @Test
    public void should_returnNotnull_when_getRecordFromDBbyMediaUri() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            Record record = mRecorderDbUtil.getRecordFromDBbyMediaUri(newUri);
            Assert.assertNull(record);
        }
    }

    @Test
    public void should_returnNotnull_when_qureyRecordByPathAndMd5() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            Record record = mRecorderDbUtil.qureyRecordByPathAndMd5("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3", "123");
            Assert.assertNull(record);
        }
    }

    @Test
    public void should_returnNotnull_when_getAmpStringByPath() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            String record = mRecorderDbUtil.getAmpStringByPath("emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3", false);
            Assert.assertNull(record);
        }
    }

    @Test
    public void should_returnNotnull_when_getMarkStringByPath() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            String record = mRecorderDbUtil.getMarkStringByPath("emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3");
            Assert.assertNull(record);
        }
    }

    @Test
    public void should_returnNotnull_when_getKeyIdByPath() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            String record = RecorderDBUtil.getKeyIdByPath("emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3");
            Assert.assertNotNull(record);
        }
    }

    @Test
    public void should_returnNotnull_when_getKeyIdByPath2() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            String record = RecorderDBUtil.getKeyIdByPath("emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3", "456.mp3");
            Assert.assertNotNull(record);
        }
    }

    @Test
    public void should_returnNotnull_when_getCreateTimeByPath() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            long id = ContentUris.parseId(newUri);
            Long record = mRecorderDbUtil.getCreateTimeByPath(id, false);
            Assert.assertNotNull(record);
        }
    }

    @Test
    public void should_returnNotnull_when_hasDirtyData() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            boolean insertResult = mRecorderDbUtil.hasDirtyData();
            Assert.assertFalse(insertResult);
        }
    }

    @Test
    public void should_returnNotnull_when_getDirtyDataForBatchUploadFile() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            List<Record> records = mRecorderDbUtil.getDirtyDataForBatchUploadFile();
            Assert.assertEquals(records.size(), 0);
        }
    }

    @Test
    public void should_returnNotnull_when_getDirtyDataForBatchUploadMegaData() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            List<Record> records = mRecorderDbUtil.getDirtyDataForBatchUploadMegaData();
            Assert.assertEquals(records.size(), 0);
        }
    }

    @Test
    public void should_returnNotnull_when_getDataForBatchDownloadFile() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            CloudSyncRecorderDbUtil.clearLocalSyncStatusForLogingout(mContext);
            List<Record> records = mRecorderDbUtil.getDataForBatchDownloadFile();
            Assert.assertEquals(records.size(), 0);
        }
    }

    @Test
    public void should_returnFalse_when_updateSyncStateByUUid() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            boolean isupdata = mRecorderDbUtil.updateSyncStateByUUid("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3", 1);
            Assert.assertFalse(isupdata);
        }
    }

    @Test
    public void should_returnFalse_when_updateDownloadStateByUUid() {
        Record record = Mockito.mock(Record.class);
        record.setData("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3");
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            boolean isupdata = mRecorderDbUtil.updateDownloadStateByUUid(UUID.randomUUID().toString(), 1);
            Assert.assertFalse(isupdata);
        }
    }

    @Test
    public void should_returnNotnull_when_updateRecordByUUid() throws Exception {
        Record record = Mockito.mock(Record.class);
        record.setData("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3");
        ContentValues contentValues = genValues();
        boolean isupdata = Whitebox.invokeMethod(mRecorderDbUtil, "updateRecordByUUid", UUID.randomUUID().toString(), contentValues);
        Assert.assertTrue(isupdata);
    }

    @Test
    public void should_returnNotnull_when_generateContentValuesForInputSyncStatus() throws Exception {
        Record record = Mockito.mock(Record.class);
        record.setData("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3");
        ContentValues contentValues = Whitebox.invokeMethod(mRecorderDbUtil, "generateContentValuesForInputSyncStatus", record, 2);
        Assert.assertNotNull(contentValues);
    }

    @Test
    public void should_returnNotnull_when_generateContentValuesForDownloadStatus() throws Exception {
        Record record = Mockito.mock(Record.class);
        record.setData("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3");
        ContentValues contentValues = Whitebox.invokeMethod(mRecorderDbUtil, "generateContentValuesForDownloadStatus", record, 2);
        Assert.assertNotNull(contentValues);
    }

    @Test
    public void should_returnNotnull_when_getAllData() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            List<Record> records = mRecorderDbUtil.getAllData();
            Assert.assertEquals(records.size(), 1);
        }
    }

    @Test
    public void should_returnFalse_when_updateMD5AndSizeForFile() {
        Record record = Mockito.mock(Record.class);
        record.setData("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3");
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            boolean isupdata = mRecorderDbUtil.updateMD5AndSizeForFile(new File("emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3"));
            Assert.assertFalse(isupdata);
        }
    }

    @Test
    public void should_list_when_getToDeletePaths() throws Exception {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            List<String> stringList = Whitebox.invokeMethod(CloudSyncRecorderDbUtil.class, "getToDeletePaths", mContext);
            Assert.assertNotNull(stringList);
        }
    }

    @Test
    public void should_returnNotnull_when_getRecordByFileIdForDownlaodResult() {
        Record record = Mockito.mock(Record.class);
        record.setData("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3");
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            List<Record> records = mRecorderDbUtil.getRecordByFileIdForDownlaodResult(UUID.randomUUID().toString());
            Assert.assertEquals(records.size(), 0);
        }
    }

    @Test
    public void should_returnFalse_when_updateRealFileSizeOrMd5() {
        Record record = mRecorderDbUtil.getRecordById("1");
        if (record == null) {
            record = Mockito.mock(Record.class);
            record.setData("emulated\\0\\Music\\Recordings\\Standard Recordings\\123.mp3");
            ContentValues contentValues = genValues();
            Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
            if (newUri != null) {
                boolean isupdata = mRecorderDbUtil.updateRealFileSizeOrMd5(record, new File("emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3"), true);
                Assert.assertFalse(isupdata);
            }
        }
    }

    @Test
    public void should_returnNotnull_when_insertInitRecordIntoDbForStartRecord() {
        Record record = mRecorderDbUtil.insertInitRecordIntoDbForStartRecord("456.mp3", 1234L, "2",
                "emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3", "emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3", 2);
        Assert.assertNotNull(record);
    }

    @Test
    public void should_returnNotnull_when_updateRecordDataWithCloudMark() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            long id = ContentUris.parseId(newUri);
            String selection = COLUMN_NAME_ID + " = " + id;
            ContentValues newContentValues = new ContentValues();
            newContentValues.put(COLUMN_NAME_MARK_DATA, "1111".getBytes());
            newContentValues.put(COLUMN_NAME_DISPLAY_NAME, "456.mp3");
            newContentValues.put(COLUMN_NAME_DATA, "emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3");
            int length = RecorderDBUtil.updateRecordDataWithCloudMark(mContext, "1", newContentValues, selection, null);
            Assert.assertEquals(length, 1);
        }
    }

    @Test
    public void should_returnFalse_when_insertCloudMetadataForLocallyExistsFile() {
        Record record = mRecorderDbUtil.insertInitRecordIntoDbForStartRecord("456.mp3", 1234L, "2",
                "emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3", "emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3", 2);
        boolean insertResult = CloudSyncRecorderDbUtil.insertCloudMetadataForLocallyExistsFile(record);
        Assert.assertFalse(insertResult);
    }

    @Test
    public void should_returnFalse_when_processDecrypAudioFile() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            boolean insertResult = mRecorderDbUtil.processDecrypAudioFile("456.mp3", "emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3", "");
            Assert.assertFalse(insertResult);
        }
    }

    @Test
    public void should_returnFalse_when_processEncrypDeleteAudioFile() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            boolean insertResult = mRecorderDbUtil.processEncrypDeleteAudioFile("456.mp3", "emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3",
                    "TEST");
            Assert.assertFalse(insertResult);
        }
    }

    @Test
    public void should_returnFalse_when_processEncryptAudioFile() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            boolean insertResult = CloudSyncRecorderDbUtil.processEncryptAudioFile("456.mp3", "emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3",
                    "TEST");
            Assert.assertFalse(insertResult);
        }
    }

    @Test
    public void should_returnFalse_when_getWhereClauseFromRecorderType() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            String insertResult = RecorderDBUtil.getWhereClauseFromRecorderType(mContext, RecordModeConstant.RECORD_TYPE_CALL, sSupportMimeType);
            Assert.assertNotNull(insertResult);
        }
        Assert.assertNotNull(RecorderDBUtil.getCallRecordWhereClauseForRecord(mContext));
    }

    @Test
    public void should_getSize_when_getWhereClauseFromRecorderType() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            Assert.assertEquals(mRecorderDbUtil.clearFailedCount(-1), 0);
            Assert.assertNotNull(mRecorderDbUtil.queryNeedDecodeAmplitudeRecordFile());
            Record record = mRecorderDbUtil.insertInitRecordIntoDbForStartRecord("456.mp3", 1234L, "2",
                    "emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3", "emulated\\0\\Music\\Recordings\\Standard Recordings\\456.mp3", 2);
            Assert.assertEquals(mRecorderDbUtil.updateRenameRecordDB(record), 1);
            Assert.assertNotNull(mRecorderDbUtil.queryMaybeRenameRecordFiles(record));
            Assert.assertEquals(mRecorderDbUtil.getNeedSyncCount(), 0);
        }
    }

    @Test
    public void should_getSize_when_markAsWaitingToUploadForUpdateById() {
        ContentValues contentValues = genValues();
        Uri newUri = RecorderDBUtil.insertRecordData(mContext, contentValues);
        if (newUri != null) {
            Assert.assertTrue(CloudSyncRecorderDbUtil.markAsWaitingToUploadForUpdateById(1L, false, false));
            Assert.assertTrue(CloudSyncRecorderDbUtil.updateGlobalIdAndFileIdForDelete(1L, "abcdefg", "1"));
            Assert.assertTrue(CloudSyncRecorderDbUtil.markAsWaitingToUploadForDeleteById(1L, true, true));
        }
    }

    @Test
    public void should_notNull_when_getRelativePathForData() {
        String relativePath = RecorderDBUtil.getRelativePathForData(null, "1.mp3");
        Assert.assertEquals("", relativePath);

        relativePath = RecorderDBUtil.getRelativePathForData(MP3_PATH_TEXT, null);
        Assert.assertEquals("", relativePath);

        relativePath = RecorderDBUtil.getRelativePathForData(MP3_PATH_TEXT, "1.mp3");
        Assert.assertEquals(relativePath, "emulated/0/Music/Recordings/Standard Recordings/");
    }

    @Test
    public void should_when_checkLocalHasAmp() {
        boolean hasAmp = mRecorderDbUtil.checkLocalHasAmp(null);
        Assert.assertFalse(hasAmp);
    }
}
