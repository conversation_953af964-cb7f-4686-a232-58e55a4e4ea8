package io.noties.markwon.utils;

import androidx.annotation.NonNull;

import org.commonmark.node.Node;

import java.util.regex.Pattern;

/**
 * @since 4.6.0
 */
public abstract class ParserUtils {

    public final static String REGEX = "^(\\d+|[一二三四五六七八九十]|[a-zA-Z])\\.?(\\s)?$";
    public static void moveChildren(@NonNull Node to, @NonNull Node from) {
        Node next = from.getNext();
        Node temp;
        while (next != null) {
            // appendChild would unlink passed node (thus making next info un-available)
            temp = next.getNext();
            to.appendChild(next);
            next = temp;
        }
    }

    private ParserUtils() {
    }

    public static boolean isValidIndex(String input) {
        // 正则表达式匹配 (数字|汉字|字母)(\.|空格)? 用于判断序号
        Pattern pattern = Pattern.compile(REGEX);
        return pattern.matcher(input).matches();
    }
}
