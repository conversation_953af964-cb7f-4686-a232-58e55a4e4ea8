/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ListItemProps
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package io.noties.markwon.prop

import io.noties.markwon.Prop
import io.noties.markwon.core.MarkwonTheme

abstract class SpaceProps {

    companion object {
        const val LEVEL_NO_SPACE = 0
        const val LEVEL_SPACE_SMALL = 1
        const val LEVEL_SPACE_NORMAL = 2
        const val LEVEL_SPACE_LARGE = 3
        const val LEVEL_SPACE_BIG_LARGE = 4
        val LIST_ITEM_TOP_SPACE: Prop<Int> = Prop.of<Int>("list-item-top-space-level")

        @JvmStatic
        fun getSpace(theme: MarkwonTheme, topSpaceLevel: Int): Int {
            return when(topSpaceLevel) {
                LEVEL_SPACE_SMALL -> theme.getBlockTopSpace()[0]
                LEVEL_SPACE_NORMAL -> theme.getBlockTopSpace()[1]
                LEVEL_SPACE_LARGE -> theme.getBlockTopSpace()[2]
                LEVEL_SPACE_BIG_LARGE -> theme.getBlockTopSpace()[3]
                else -> 0
            }
        }
    }
}