/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CustomParagraphSpan
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/07
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package io.noties.markwon.core.spans

import android.graphics.Paint
import android.text.style.LineHeightSpan
import io.noties.markwon.core.MarkwonTheme
import io.noties.markwon.utils.SpaceHelper

class CustomParagraphSpan(private val theme: MarkwonTheme, private val spaceLineLevel: Int) :
    LineHeightSpan {
    private val spaceHelper by lazy {
        SpaceHelper(theme, spaceLineLevel, "CustomParagraphSpan")
    }

    override fun chooseHeight(
        text: CharSequence?,
        start: Int,
        end: Int,
        spanStartV: Int,
        v: Int,
        fontMetricsInt: Paint.FontMetricsInt?
    ) {
        spaceHelper.chooseHeight(text, start, end, spanStartV, v, fontMetricsInt)
    }
}