package io.noties.markwon.ext.tasklist;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.text.Layout;
import android.text.style.LeadingMarginSpan;
import android.text.style.LineHeightSpan;

import androidx.annotation.NonNull;
import io.noties.markwon.core.AnimationSpan;
import io.noties.markwon.core.MarkwonTheme;
import io.noties.markwon.utils.LeadingMarginUtils;
import io.noties.markwon.utils.SpaceHelper;

/**
 * @since 1.0.1
 */
public class TaskListSpan implements LeadingMarginSpan, AnimationSpan, LineHeightSpan {

    private static final int[] STATE_CHECKED = new int[]{android.R.attr.state_checked};

    private static final int[] STATE_NONE = new int[0];

    private final MarkwonTheme theme;
    private final Drawable drawable;
    private final Drawable checkDrawable;

    // @since 2.0.1 field is NOT final (to allow mutation)
    private boolean isDone;
    private String task;
    private Boolean mVisible = true;
    private float mFraction = 1f;
    private final SpaceHelper mSpaceUtils;


    public TaskListSpan(@NonNull MarkwonTheme theme, @NonNull Drawable drawable, boolean isDone, String task, int spaceLevel) {
        this.theme = theme;
        this.drawable = drawable;
        this.isDone = isDone;
        this.task = task;
        this.checkDrawable = drawable;
        mSpaceUtils = new SpaceHelper(theme, spaceLevel, "TaskListSpan");
    }

    public TaskListSpan(@NonNull MarkwonTheme theme, @NonNull Drawable drawable, @NonNull Drawable checkoutDrawable, boolean isDone, String task, int spaceLevel) {
        this.theme = theme;
        this.drawable = drawable;
        this.isDone = isDone;
        this.task = task;
        this.checkDrawable = checkoutDrawable;
        mSpaceUtils = new SpaceHelper(theme, spaceLevel, "TaskListSpan");
    }

    /**
     * @since 2.0.1
     */
    public boolean isDone() {
        return isDone;
    }

    /**
     * Update {@link #isDone} property of this span. Please note that this is merely a visual change
     * which is not changing underlying text in any means.
     *
     * @since 2.0.1
     */
    public void setDone(boolean isDone) {
        this.isDone = isDone;
    }

    public String getTask() {
        return this.task;
    }

    public void setTask(String task) {
        this.task = task;
    }

    @Override
    public int getLeadingMargin(boolean first) {
        return theme.getBlockMargin();
    }

    @Override
    public void drawLeadingMargin(Canvas c, Paint p, int x, int dir, int top, int baseline, int bottom, CharSequence text, int start, int end, boolean first, Layout layout) {
        if (!first
                || !LeadingMarginUtils.selfStart(start, text, this)) {
            return;
        }

        final float descent = p.descent();
        final float ascent = p.ascent();

        final int save = c.save();
        try {

            final int width = theme.getBlockMargin();
            final int height = (int) (descent - ascent + 0.5F);
            if (checkDrawable == drawable) {
                final int w = (int) (width * .75F + .5F);
                final int h = (int) (height * .75F + .5F);

                if (drawable.isStateful()) {
                    final int[] state;
                    if (isDone) {
                        state = STATE_CHECKED;
                    } else {
                        state = STATE_NONE;
                    }
                    drawable.setState(state);
                }

                drawable.setBounds(0, 0, w, h);

                final int l;
                if (dir > 0) {
                    l = x + ((width - w) / 2);
                } else {
                    l = x - ((width - w) / 2) - w;
                }

                final int t = (int) (baseline + ascent + 0.5F) + ((height - h) / 2);
                setAlpha(drawable);
                c.translate(l, t);
                drawable.draw(c);
            } else {
                Drawable dealDrawable;
                if (isDone) {
                    dealDrawable = checkDrawable.mutate();
                } else {
                    dealDrawable = drawable.mutate();
                }
                final int w = dealDrawable.getIntrinsicWidth();
                final int h = dealDrawable.getIntrinsicHeight();
                dealDrawable.setBounds(0, 0, w, h);

                final int l;
                if (dir > 0) {
                    l = x + ((width - w) / 2);
                } else {
                    l = x - ((width - w) / 2) - w;
                }

                final int t = (int) (baseline + ascent + 0.5F) + ((height - h) / 2);
                c.translate(l - 8, t);
                if (mVisible) {
                    dealDrawable.draw(c);
                }
            }
        } finally {
            c.restoreToCount(save);
        }
    }

    private void setAlpha(Drawable drawable) {
        if (mVisible) {
            drawable.setAlpha(255);
        } else {
            drawable.setAlpha(0);
        }
    }

    @Override
    public void visible() {
        mVisible = true;
    }

    @Override
    public void invisible() {
        mVisible = false;
    }

    @Override
    public void updateFraction(float fraction) {
        mFraction = fraction;
        if (mFraction > 0f) {
            visible();
        }
    }

    @Override
    public void chooseHeight(CharSequence text, int start, int end, int spanStartV, int v, Paint.FontMetricsInt fontMetricsInt) {
        mSpaceUtils.chooseHeight(text, start, end, spanStartV, v, fontMetricsInt);
    }
}
