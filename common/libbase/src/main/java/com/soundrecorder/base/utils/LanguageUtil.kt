/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  LanguageUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.utils

import android.content.Context
import android.content.res.Configuration
import android.os.LocaleList
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.reflect.TypeToken
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.R
import java.util.Locale
import java.util.concurrent.ConcurrentHashMap

object LanguageUtil {

    private const val TAG = "LanguageUtil"

    private const val LANG_ZH = "zh"
    private const val LANG_ZH_TW = "zh-TW"
    private const val LANG_ZH_HK = "zh-HK"
    private const val LANG_EN = "en"
    private const val LANG_HI = "hi"
    private const val LANG_ES = "es"
    private const val LANG_ES_MX = "es-MX"
    private const val LANG_IT = "it"
    private const val LANG_ID = "id"
    private const val LANG_TH = "th"
    private const val LANG_AR = "ar"
    private const val LANG_VI = "vi"
    private const val LANG_MS = "ms"
    private const val LANG_PT_BR = "pt-BR"
    private const val LANG_RU = "ru"
    private const val LANG_FIL = "fil"
    private const val LANG_JA = "ja"
    private const val LANG_FR = "fr"
    private const val LANG_TR = "tr"
    private const val LANG_PL = "pl"

    // 配置文件路径
    private const val LANGUAGE_CONFIG_FILE = "realtime_asr_language_config.json"

    // JSON字段名
    private const val FIELD_ASR_KIT_CONF = "ai_asr_kit_conf"
    private const val FIELD_ASR_LLM_CONF = "ai_text_generate_conf"
    private const val FIELD_DOMESTIC = "domestic"
    private const val FIELD_EXPORT = "export"
    private const val FIELD_EXPECTED_WORD_COUNT = "expected_word_count"

    // 语种预期字数的map
    private val mExpectedWordCountMap = ConcurrentHashMap<String, Int>()
    // 默认预期字数
    const val DEFAULT_EXPECTED_WORDS = 110

    @JvmStatic
    fun getCurrentLanguageFromSystem(): String {
        var language = ""
        val localeList = LocaleList.getDefault()
        if (localeList.size() > 0) {
            val locale = localeList[0]
            if (locale != null) {
                language = locale.language
            }
        }
        DebugUtil.i(TAG, "CurrentLanguage: $language")
        return language
    }

    /**
     * 简体中文
     */
    @JvmStatic
    fun isZHCN(): Boolean {
        val locale = Locale.getDefault()
        val isZH = locale.language.equals("zh", true)
        val isCN = locale.country.equals("CN", true)
        return isZH && isCN
    }

    /**
     * 繁体中文，中国台湾地区使用
     */
    @JvmStatic
    fun isZHTW(): Boolean {
        val locale = Locale.getDefault()
        val isZH = locale.language.equals("zh", true)
        val isTW = locale.country.equals("TW", true)
        return isZH && isTW
    }

    /**
     * 繁体中文，中国香港以及其它中文地区
     */
    @JvmStatic
    fun isZHHK(): Boolean {
        val locale = Locale.getDefault()
        val isZH = locale.language.equals("zh", true)
        val isHK = locale.country.equals("HK", true)
        return isZH && isHK
    }

    /**
     * 西班牙（墨西哥）
     */
    @JvmStatic
    fun isESMX(): Boolean {
        val locale = Locale.getDefault()
        val isES = locale.language.equals("es", true)
        val isMX = locale.country.equals("MX", true)
        return isES && isMX
    }

    /**
     * 维吾尔语
     */
    @JvmStatic
    fun isUG(): Boolean = Locale.getDefault().language.equals("ug", true)

    /**
     * 藏语
     */
    @JvmStatic
    fun isBO(): Boolean = Locale.getDefault().language.equals("bo", true)

    /**
     * 缅甸语
     */
    @JvmStatic
    fun isMY(): Boolean = Locale.getDefault().language.equals("my", true)

    /**
     * 获取当前系统语言
     */
    @JvmStatic
    fun getLocalLanguage(): String = Locale.getDefault().language

    /**
     * 语种映射
     * 注意，配置本地语种列表时，一定要注意配置的语种是否有对应的映射词条，语种映射词条见方法getLanguageDisplayName
     * 本地映射文件为 realtime_asr_language_config.json
     */
    @JvmStatic
    fun getAsrLangMap(context: Context, asrLanguage: List<String>): MutableMap<String, String> {
        val resultMap = mutableMapOf<String, String>()
        asrLanguage.forEach { langCode ->
            getLanguageDisplayName(context, langCode)?.let {
                resultMap[langCode] = it
            } ?: run {
                DebugUtil.w(TAG, "getLanguageDisplayName return null, langCode=$langCode")
                resultMap[langCode] = langCode
            }
        }
        return resultMap
    }

    /**
     * 根据语言代码获取显示名称，使用when选择器方式映射
     * @param context 上下文
     * @param languageCode 语言代码
     * @return 语言显示名称，如果不支持则返回null
     */
    @JvmStatic
    fun getLanguageDisplayName(context: Context, languageCode: String): String? {
        return when (languageCode) {
            LANG_ZH -> context.getString(R.string.summary_language_cn)
            LANG_ZH_TW -> context.getString(R.string.language_traditional_chinese)
            LANG_ZH_HK -> context.getString(R.string.language_traditional_chinese)
            LANG_EN -> context.getString(R.string.summary_language_en)
            LANG_HI -> context.getString(R.string.summary_language_hi)
            LANG_ES -> context.getString(R.string.language_spanish_spain)
            LANG_ES_MX -> context.getString(R.string.language_spanish_mexico)
            LANG_IT -> context.getString(R.string.language_italian)
            LANG_ID -> context.getString(R.string.language_bahasa_indonesia)
            LANG_TH -> context.getString(R.string.language_thai)
            LANG_AR -> context.getString(R.string.language_arabic)
            LANG_VI -> context.getString(R.string.language_vietnamese)
            LANG_MS -> context.getString(R.string.language_malay)
            LANG_PT_BR -> context.getString(R.string.language_portuguese_brazil)
            LANG_RU -> context.getString(R.string.language_russia)
            LANG_FIL -> context.getString(R.string.language_filipino)
            LANG_JA -> context.getString(R.string.language_japanese)
            LANG_FR -> context.getString(R.string.language_french)
            LANG_TR -> context.getString(R.string.language_turkish)
            LANG_PL -> context.getString(R.string.language_polish)
            else -> null // 不支持的语言代码返回null
        }
    }

    /**
     * 获取区域支持的语言配置
     * 根据当前是内销还是外销返回对应的语言列表
     */
    @JvmStatic
    fun getRegionSupportedLanguages(isEXP: Boolean = BaseUtil.isEXP()): Set<String> =
        getLanguagesFromConfigFile("getRegionSupportedLanguages", FIELD_ASR_KIT_CONF, isEXP)

    /**
     * 获取ASR大模型优化支持的语种列表
     */
    @JvmStatic
    fun getAsrLlmSupportedLanguages(isEXP: Boolean = BaseUtil.isEXP()): Set<String> =
        getLanguagesFromConfigFile("getAsrLlmSupportedLanguages", FIELD_ASR_LLM_CONF, isEXP)

    @JvmStatic
    private fun getLanguagesFromConfigFile(method: String, confField: String, isEXP: Boolean): Set<String> = runCatching {
        val gson = Gson()
        val inputStream = BaseApplication.getAppContext().assets.open(LANGUAGE_CONFIG_FILE)
        val jsonString = inputStream.bufferedReader().use { it.readText() }
        val jsonObject = gson.fromJson(jsonString, JsonObject::class.java)
        // 根据是否外销选择不同的字段
        val regionField = if (isEXP) FIELD_EXPORT else FIELD_DOMESTIC
        val languagesArray = jsonObject.getAsJsonObject(confField).getAsJsonArray(regionField)
        // 将JsonArray转换为Set<String>
        val type = object : TypeToken<Set<String>>() {}.type
        gson.fromJson<Set<String>>(languagesArray, type) ?: emptySet()
    }.getOrElse { e ->
        DebugUtil.e(TAG, "$method: Failed to load language config: ${e.message}")
        emptySet() // 如果配置文件有问题，返回空集合
    }

    /**
     * 获取各语种的预期字数配置
     * @return 包含语种代码和对应字数的Map，如 {"zh": 110, "en": 230}
     */
    @JvmStatic
    fun getExpectedWordCounts(): Map<String, Int> {
        return runCatching {
            val inputStream = BaseApplication.getAppContext().assets.open(LANGUAGE_CONFIG_FILE)
            val jsonString = inputStream.bufferedReader().use { it.readText() }
            val jsonObject = Gson().fromJson(jsonString, JsonObject::class.java)
            val expectedWordCount = jsonObject.getAsJsonObject(FIELD_EXPECTED_WORD_COUNT)
            val type = object : TypeToken<Map<String, Int>>() {}.type
            Gson().fromJson<Map<String, Int>>(expectedWordCount, type) ?: emptyMap()
        }.getOrElse {
            DebugUtil.e(TAG, "Failed to parse expected_word_count, error: ${it.message}")
            emptyMap() // 出现异常时返回空Map
        }
    }

    @JvmStatic
    fun getExpectedWords(languageCode: String?): Int {
        languageCode ?: return DEFAULT_EXPECTED_WORDS
        if (mExpectedWordCountMap.isEmpty()) {
            mExpectedWordCountMap.putAll(getExpectedWordCounts())
        }
        return mExpectedWordCountMap[languageCode] ?: DEFAULT_EXPECTED_WORDS
    }

    @JvmStatic
    fun getAsrDefaultLanguage(): String {
        return if (BaseUtil.isEXP()) {
            LANG_EN
        } else {
            LANG_ZH
        }
    }

    /**
     * 获取默认支持的语言 如果系统语言不支持，则返回默认语言(内销中文，外销英文)
     * @param supportLanguageList 支持的语言列表
     * @return 默认支持的语言
     */
    @JvmStatic
    fun getAsrDefaultSupportLanguageWithLocal(supportLanguageList: List<String>): String {
        val localLanguage = getLocalLanguage()
        if (supportLanguageList.contains(localLanguage)) {
            DebugUtil.d(TAG, "getAsrDefaultSupportLanguageWithLocal return localLanguage=$localLanguage")
            return localLanguage
        }
        val defaultLanguage = getAsrDefaultLanguage()
        DebugUtil.d(TAG, "getAsrDefaultSupportLanguageWithLocal return defaultLanguage=$defaultLanguage")
        return defaultLanguage
    }

    /**
     * 通过创建英文Locale的Context来获取strings资源中的英文翻译
     * @param languageCode 语言代码
     * @return 语言的英文名称，如果不支持则返回语言代码本身
     */
    @JvmStatic
    fun getLanguageEnglishName(context: Context, languageCode: String): String {
        return runCatching {
            // 创建英文Locale的Context
            val englishContext = createEnglishContext(context)
            // 使用英文Context获取对应的字符串资源
            getLanguageDisplayName(englishContext, languageCode) ?: languageCode
        }.getOrElse {
            DebugUtil.e(TAG, "Failed to get English name for language code: $languageCode, error: ${it.message}")
            languageCode // 出现异常时返回原始代码
        }
    }

    /**
     * 创建英文Locale的Context，用于获取英文字符串资源
     * @param context 原始Context
     * @return 英文Locale的Context
     */
    @JvmStatic
    private fun createEnglishContext(context: Context): Context {
        val englishLocale = Locale.ENGLISH
        val configuration = Configuration(context.resources.configuration)
        configuration.setLocale(englishLocale)
        return context.createConfigurationContext(configuration)
    }

    /**
     * 对语种列表进行排序，系统语言、中文和英文靠前显示，其余语言按照英文名称A-Z排序
     * @param languageList 语种列表
     * @return 排序后的语种列表
     */
    @JvmStatic
    fun sortLanguageList(context: Context, languageList: List<String>): List<String> {
        if (languageList.isEmpty()) {
            return languageList
        }
        val sortedList = mutableListOf<String>()
        val remainingLanguages = languageList.toMutableList()
        // 优先处理系统语言
        var systemLanguage = getLocalLanguage()
        // 特殊城市处理
        when (systemLanguage) {
            LANG_ZH -> {
                DebugUtil.d(TAG, "sortLanguageList systemLanguage is zh, isHK=${isZHHK()}, isTW=${isZHTW()}")
                if (isZHHK() || isZHTW()) {
                    systemLanguage = LANG_ZH_TW
                }
            }

            LANG_ES -> {
                DebugUtil.d(TAG, "sortLanguageList systemLanguage is es, isMX=${isESMX()}")
                if (isESMX()) {
                    systemLanguage = LANG_ES_MX
                }
            }
        }
        if (remainingLanguages.contains(systemLanguage)) {
            sortedList.add(systemLanguage)
            remainingLanguages.remove(systemLanguage)
        }
        // 其次处理中文（简体）
        if (remainingLanguages.contains(LANG_ZH)) {
            sortedList.add(LANG_ZH)
            remainingLanguages.remove(LANG_ZH)
        }
        // 然后处理英文
        if (remainingLanguages.contains(LANG_EN)) {
            sortedList.add(LANG_EN)
            remainingLanguages.remove(LANG_EN)
        }
        // 剩余语言按照英文名称A-Z排序
        val sortedRemaining = remainingLanguages.sortedBy { languageCode ->
            getLanguageEnglishName(context, languageCode)
        }
        sortedList.addAll(sortedRemaining)
        DebugUtil.d(TAG, "sortLanguageList: input=${languageList.size}, output=${sortedList.size} sorted order=$sortedList")
        return sortedList
    }
}