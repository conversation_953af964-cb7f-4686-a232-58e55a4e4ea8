/*********************************************************************
 * Copyright (C), 2010-2020, Oplus mobile comm crop. All rights reserved.
 ** File        :ActionModeData
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/08/21
 ** Author      : W9095535
 **
 **  ---------------------Revision History: ----------------------------
 ** <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.actionmode

import android.view.ActionMode
import android.widget.TextView
import java.lang.ref.WeakReference

object ActionModeData {
    var actionTextView: WeakReference<TextView>? = null
    var actionMode: WeakReference<ActionMode>? = null

    fun hasData(): Boolean {
        return actionTextView?.get() != null && actionMode?.get() != null
    }

    fun finish() {
        actionMode?.get()?.finish()
        release()
    }

    fun release() {
        actionTextView = null
        actionMode = null
    }
}