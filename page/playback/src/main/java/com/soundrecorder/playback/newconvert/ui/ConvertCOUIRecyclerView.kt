/*********************************************************************
 * Copyright (C), 2010-2020, Oplus mobile comm crop. All rights reserved.
 ** File        :ConvertCOUIRecyclerView.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/08/21
 ** Author      : W9095535
 **
 **  ---------------------Revision History: ----------------------------
 ** <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.ui

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.recyclerview.widget.COUIRecyclerView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.playback.actionmode.ActionModeData

class ConvertCOUIRecyclerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : COUIRecyclerView(context, attrs, defStyleAttr) {
    companion object {
        private const val TAG = "ConvertCOUIRecyclerView"
    }

    override fun onTouchEvent(e: MotionEvent?): Boolean {
        if (ActionModeData.hasData() && e?.action == MotionEvent.ACTION_DOWN) {
            // 检查是否点击在ActionMode控件区域外
            val rect = Rect()
            ActionModeData.actionTextView?.get()?.getGlobalVisibleRect(rect)
            val overArea = !rect.contains(e.rawX.toInt(), e.rawY.toInt())
            DebugUtil.d(TAG, "handleTouchEvent, overArea:$overArea")
            if (overArea) {
                ActionModeData.finish()
            }
        }
        return super.onTouchEvent(e)
    }
}