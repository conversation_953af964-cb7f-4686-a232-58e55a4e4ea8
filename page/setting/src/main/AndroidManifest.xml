<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.soundrecorder.setting">

    <application>
        <activity
            android:name=".setting.SettingRecorderActivity"
            android:configChanges="locale|keyboardHidden|screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:label="@string/app_name_main"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment" />

        <activity
            android:name=".about.RecordAboutActivity"
            android:configChanges="locale|orientation|keyboardHidden"
            android:exported="false"
            android:label="@string/app_name_main"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment" />
        <activity
            android:name=".opensource.OpenSourceActivity"
            android:configChanges="locale|keyboardHidden|screenSize|smallestScreenSize|screenLayout|orientation"
            android:exported="false"
            android:label="@string/app_name_main"
            android:screenOrientation="behind"
            android:theme="@style/AppBaseTheme.NoActionBar.ActionMode" />

        <activity
            android:name=".privacypolicy.PrivacyPolicyActivity"
            android:exported="false"
            android:label="@string/app_name_main"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment" />

        <activity
            android:name=".modelinfo.LargeModelInfoActivity"
            android:exported="false"
            android:label="@string/app_name_main"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment" />

        <activity
            android:name=".setting.AigcSettingActivity"
            android:configChanges="locale|keyboardHidden|screenSize|smallestScreenSize|screenLayout|orientation"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleTop"
            android:permission="com.oplus.permission.safe.SETTINGS"
            android:screenOrientation="behind"
            android:theme="@style/AppNoTitleTheme.PreferenceFragment">
            <!-- 业务方activity跳转action -->
            <intent-filter>
                <action android:name="com.oplus.setting.ai.action.SETTING_ENTRANCE" />
                <category android:name="android.intent.category.DEFAULT" />
                <action android:name="android.intent.action.MAIN" />
            </intent-filter>
            <!-- 业务方ID -->
            <meta-data
                android:name="com.oplus.setting.ai.ID"
                android:value="0020060000" />
            <!-- 业务方icon -->
            <meta-data
                android:name="com.oplus.setting.ai.ICON"
                android:resource="@drawable/setting_aigc_icon" />
            <!-- 业务方名称 -->
            <meta-data
                android:name="com.oplus.setting.ai.FUNCTION_NAME"
                android:resource="@string/setting_aigc_title" />
            <!-- 业务方辅助信息 -->
            <meta-data
                android:name="com.oplus.setting.ai.AUXILIARY_TEXT"
                android:resource="@string/setting_aigc_desc" />
            <!-- 业务方是否显示feature控制，可选 -->
            <meta-data
                android:name="com.oplus.setting.ai.FEATUE"
                android:value="[app].[enable].[com_oplus_soundrecord_aigc_settings]" />
        </activity>
    </application>

</manifest>