/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : HighlightablePreferenceGroupAdapter.kt
 ** Description : HighlightablePreferenceGroupAdapter.kt
 ** Version     : 1.0
 ** Date        : 2025/08/11
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  Jiafei.Liu     2025/08/11     1.0      create
 ***********************************************************************/
package com.soundrecorder.setting.setting

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.text.TextUtils
import android.view.View
import androidx.preference.PreferenceGroup
import androidx.preference.PreferenceGroupAdapter
import androidx.preference.PreferenceViewHolder
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.cardlist.COUICardListSelectedItemLayout
import com.coui.appcompat.contextutil.COUIContextUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NightModeUtil.isNightMode
import com.soundrecorder.setting.R

@SuppressLint("RestrictedApi")
class HighlightablePreferenceGroupAdapter(
    preferenceGroup: PreferenceGroup?,
    highlightKey: String?,
    highlightRequested: Boolean
) : PreferenceGroupAdapter(preferenceGroup) {
    companion object {
        private const val TAG = "HighlightablePreferenceGroupAdapter"
        const val EXTRA_FRAGMENT_ARG_KEY = ":settings:fragment_args_key"
        private const val DELAY_COLLAPSE_DURATION_MILLIS = 300L
        private const val DELAY_HIGHLIGHT_DURATION_MILLIS = 300L
        private const val HIGHLIGHT_DURATION = 500L
        private const val HIGHLIGHT_FADE_OUT_DURATION = 500L
        private const val HIGHLIGHT_FADE_IN_DURATION = 500L
        private const val HIGH_LIGHT_COLOR_PREFERENCE_DEFAULT = 0xFFE4E4E4
        private const val HIGH_LIGHT_COLOR_PREFERENCE_DARK = 0xFF333333
    }

    private var mNormalBackgroundRes = 0
    private var mHighlightKey: String? = null
    private var mHighlightColor = 0
    private var mFadeInAnimated = false
    private var mHighlightRequested = false
    private var mHighlightPosition = RecyclerView.NO_POSITION

    init {
        mHighlightKey = highlightKey
        mHighlightRequested = highlightRequested
        val context = preferenceGroup?.context
        mNormalBackgroundRes = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorCardBackground)
        mHighlightColor = if (isNightMode(context)) HIGH_LIGHT_COLOR_PREFERENCE_DARK.toInt() else HIGH_LIGHT_COLOR_PREFERENCE_DEFAULT.toInt()
    }

    override fun onBindViewHolder(holder: PreferenceViewHolder, position: Int) {
        super.onBindViewHolder(holder, position)
        updateBackground(holder, position)
    }

    private fun updateBackground(holder: PreferenceViewHolder, position: Int) {
        val v: View = holder.itemView
        if (position == mHighlightPosition && mHighlightKey != null && TextUtils.equals(mHighlightKey, getItem(position).key)) {
            // This position should be highlighted. If it's highlighted before - skip animation.
            addHighlightBackground(holder, !mFadeInAnimated)
        } else if (java.lang.Boolean.TRUE == v.getTag(R.id.preference_highlighted)) {
            // View with highlight is reused for a view that should not have highlight
            removeHighlightBackground(holder, false)
        }
    }

    fun forceHighlight() {
        mHighlightRequested = false
        mFadeInAnimated = false
    }

    private fun requestRemoveHighlightDelayed(holder: PreferenceViewHolder) {
        val v = holder.itemView
        v.postDelayed(
            {
                mHighlightPosition = RecyclerView.NO_POSITION
                removeHighlightBackground(
                    holder, true
                )
            },
            HIGHLIGHT_DURATION
        )
    }

    private fun addHighlightBackground(holder: PreferenceViewHolder, animate: Boolean) {
        val v = holder.itemView
        v.setTag(R.id.preference_highlighted, true)
        if (!animate) {
            updateBackgroundColor(v, mHighlightColor)
            DebugUtil.d(TAG, "AddHighlight: Not animation requested - setting highlight background")
            requestRemoveHighlightDelayed(holder)
            return
        }
        mFadeInAnimated = true
        val colorFrom = mNormalBackgroundRes
        val colorTo = mHighlightColor
        val fadeInLoop = ValueAnimator.ofObject(ArgbEvaluator(), colorFrom, colorTo)
        fadeInLoop.duration = HIGHLIGHT_FADE_IN_DURATION
        fadeInLoop.repeatMode = ValueAnimator.REVERSE
        fadeInLoop.start()
        DebugUtil.i(TAG, "AddHighlight: starting fade in animation")
        holder.setIsRecyclable(false)
        requestRemoveHighlightDelayed(holder)
    }

    private fun removeHighlightBackground(holder: PreferenceViewHolder, animate: Boolean) {
        val v = holder.itemView
        if (!animate) {
            v.setTag(R.id.preference_highlighted, false)
            kotlin.runCatching {
                v.setBackgroundColor(mNormalBackgroundRes)
            }.onFailure {
                DebugUtil.e(TAG, "removeHighlightBackground error: ${it.message}")
            }
            return
        }

        if (java.lang.Boolean.TRUE != v.getTag(R.id.preference_highlighted)) {
            // Not highlighted, no-op
            DebugUtil.d(TAG, "RemoveHighlight: Not highlighted - skipping")
            return
        }

        val colorFrom = mHighlightColor
        val colorTo = mNormalBackgroundRes
        v.setTag(R.id.preference_highlighted, false)
        val colorAnimation = ValueAnimator.ofObject(ArgbEvaluator(), colorFrom, colorTo)
        colorAnimation.duration = HIGHLIGHT_FADE_OUT_DURATION
        colorAnimation.addUpdateListener { animator: ValueAnimator -> updateBackgroundColor(v, animator.animatedValue as Int) }
        colorAnimation.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                updateBackgroundColor(v, mNormalBackgroundRes)
                holder.setIsRecyclable(true)
            }
        })
        colorAnimation.start()
        DebugUtil.i(TAG, "Starting fade out animation")
    }

    private fun updateBackgroundColor(v: View, color: Int) {
        if (v is COUICardListSelectedItemLayout) {
            v.refreshCardBg(color)
        } else {
            v.setBackgroundColor(color)
        }
    }

    /**
     * A function can highlight a specific setting in recycler view.
     * note: Before highlighting a setting, screen collapses tool bar with an animation.
     */
    fun requestHighlight(root: View?, recyclerView: RecyclerView?, highlightKey: String? = null) {
        if (highlightKey != null) {
            mHighlightKey = highlightKey
        }
        if (mHighlightRequested || recyclerView == null || TextUtils.isEmpty(mHighlightKey)) {
            return
        }
        val position = getPreferenceAdapterPosition(mHighlightKey)
        if (position < 0) {
            return
        }

        // Highlight request accepted
        mHighlightRequested = true

        // Remove the animator as early as possible to avoid a RecyclerView crash.
        recyclerView.itemAnimator = null
        // Scroll to correct position after 600 milliseconds.
        root?.postDelayed(
            {
                if (ensureHighlightPosition()) {
                    recyclerView.smoothScrollToPosition(mHighlightPosition)
                }
            },
            DELAY_HIGHLIGHT_DURATION_MILLIS
        )

        // Highlight preference after 900 milliseconds.
        root?.postDelayed(
            {
                if (ensureHighlightPosition()) {
                    notifyItemChanged(mHighlightPosition)
                }
            },
            DELAY_COLLAPSE_DURATION_MILLIS + DELAY_HIGHLIGHT_DURATION_MILLIS
        )
    }

    /**
     * Make sure we highlight the real-wanted position in case of preference position already
     * changed when the delay time comes.
     */
    private fun ensureHighlightPosition(): Boolean {
        if (TextUtils.isEmpty(mHighlightKey)) {
            return false
        }
        val position = getPreferenceAdapterPosition(mHighlightKey)
        val allowHighlight = position >= 0
        if (allowHighlight && mHighlightPosition != position) {
            DebugUtil.w(TAG, "EnsureHighlight: position has changed since last highlight request")
            // Make sure RecyclerView always uses latest correct position to avoid exceptions.
            mHighlightPosition = position
        }
        return allowHighlight
    }

    fun removeHighlightPosition() {
        mHighlightPosition = RecyclerView.NO_POSITION
    }
}