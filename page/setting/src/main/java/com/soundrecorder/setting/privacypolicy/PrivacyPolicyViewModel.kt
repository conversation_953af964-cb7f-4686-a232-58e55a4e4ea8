/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : PrivacyPolicyViewModel.kt
 ** Description : View model for privacy policy
 ** Version     : 1.0
 ** Date        : 2025/05/19
 ** Author      : <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  Jiafei.Liu     2025/08/16     1.0      create
 ***********************************************************************/
package com.soundrecorder.setting.privacypolicy

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.db.CloudSyncRecorderDbUtil
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState
import com.soundrecorder.modulerouter.playback.PlayBackInterface
import com.soundrecorder.modulerouter.utils.Injector
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * ViewModel for PrivacyPolicyActivity to handle cloud sync operations
 * and avoid memory leaks from IO coroutines in Activity
 */
class PrivacyPolicyViewModel : ViewModel() {

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    private val playbackApi by lazy {
        Injector.injectFactory<PlayBackInterface>()
    }

    // LiveData to notify UI when fragment refresh is needed
    private val _fragmentRefreshEvent = MutableLiveData<Unit>()
    val fragmentRefreshEvent: LiveData<Unit> = _fragmentRefreshEvent

    /**
     * Check if region supports cloud and cloud switch is available
     */
    private fun isRegionAndSupportCloud(): Boolean = cloudKitApi?.isSupportCloudArea() == true && cloudKitApi?.isSupportSwitch() == true

    /**
     * Handle permission granted status clearing operations
     * Uses Observer pattern to notify UI refresh without holding Fragment reference
     */
    fun handlePermissionClearingOperations() {
        // Cancel all convert tasks - this is a lightweight operation (only sets flags)
        playbackApi?.cancelAllConvertTask()

        // Handle cloud sync operations if supported
        if (isRegionAndSupportCloud()) {
            viewModelScope.launch(Dispatchers.IO) {
                val isSuccess = cloudKitApi?.setSyncSwitch(CloudSwitchState.CLOSE) ?: false
                if (isSuccess) {
                    cloudKitApi?.clearCloudGrantedStatus()
                    CloudSyncRecorderDbUtil.clearLocalSyncStatusForLogingout(BaseApplication.getAppContext())
                }

                // Notify UI to refresh using LiveData - postValue is thread-safe for background threads
                _fragmentRefreshEvent.postValue(Unit)
            }
        } else {
            // No cloud operations needed, notify UI refresh directly - use postValue for consistency
            _fragmentRefreshEvent.postValue(Unit)
        }
    }
}
