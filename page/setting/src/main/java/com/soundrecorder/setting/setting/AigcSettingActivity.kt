/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AigcSettingActivity.kt
 ** Description : AigcSettingActivity.kt
 ** Version     : 1.0
 ** Date        : 2025/07/05
 ** Author      : <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  Jiafei.Liu     2025/07/05     1.0      create
 ***********************************************************************/
package com.soundrecorder.setting.setting

import android.os.Bundle
import com.soundrecorder.common.base.PrivacyPolicyBaseActivity
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.setting.R

class AigcSettingActivity : PrivacyPolicyBaseActivity() {
    companion object {
        private const val FRAGMENT_TAG: String = "aigc_setting_fragment"
    }
    private var isResume: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_aigc_setting)
        supportFragmentManager.beginTransaction()
            .replace(R.id.aigc_root_layout, getSettingFragment(), FRAGMENT_TAG)
            .commit()
    }

    private fun getSettingFragment(): AigcSettingsFragment {
        val settingFragment = supportFragmentManager.findFragmentByTag(FRAGMENT_TAG) as? AigcSettingsFragment
        return settingFragment ?: AigcSettingsFragment()
    }

    override fun onPrivacyPolicySuccess(type: Int, pageFrom: Int?) {
        val fragments = supportFragmentManager.fragments
        for (fragment in fragments) {
            if (fragment is AigcSettingsFragment) {
                fragment.onPrivacyPolicySuccess(type)
                break
            }
        }
    }

    override fun onPrivacyPolicyFail(type: Int, pageFrom: Int?) {
        val fragments = supportFragmentManager.fragments
        for (fragment in fragments) {
            if (fragment is AigcSettingsFragment) {
                fragment.onPrivacyPolicyFail(type)
                break
            }
        }
    }

    override fun getPageType(): Int {
        return PrivacyPolicyConstant.PAGE_FROM_AIGC_SETTING
    }

    override fun onResume() {
        super.onResume()
        isResume = true
    }
    override fun onPause() {
        super.onPause()
        isResume = false
    }
    fun getResumeState(): Boolean {
        return isResume
    }
}