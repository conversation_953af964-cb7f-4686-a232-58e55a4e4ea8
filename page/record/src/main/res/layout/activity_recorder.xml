<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/recorder_header"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/recorder_background_gradient"
        android:tag="sharedView"/>

    <!--该View仅用来适配底部 taskbar 颜色适配，若后续有新的方案可处理，可删除该View-->
    <View
        android:id="@+id/view_task_bar_navigation"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_gravity="bottom"
        android:background="@color/background_taskbar_navigation"
        android:visibility="gone" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/root_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:background="@color/coui_color_label_on_color">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/abl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/coui_transparence"
            app:elevation="0dp"
            app:layout_constraintTop_toTopOf="parent">

            <com.coui.appcompat.toolbar.COUIToolbar
                android:id="@+id/toolbar"
                android:tag="toolBar"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp52" />

        </com.google.android.material.appbar.AppBarLayout>

        <!-- 大尺寸波形图区域 -->
        <com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout
            android:id="@+id/wave_gradient_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:tag="waveGradientView"
            app:layout_constraintHeight_percent="@dimen/record_wave_view_height_percent"
            android:layout_marginTop="@dimen/wave_margin_top"
            app:backgroundWhole="@color/wave_recycler_background"
            app:layout_constraintTop_toBottomOf="@id/abl">

            <com.soundrecorder.record.views.wave.RecorderWaveRecyclerView
                android:id="@+id/ruler_view"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:clickable="false"
                android:focusable="false"
                android:layoutDirection="ltr"
                android:tag="largeWaveRecyclerView" />
        </com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout>

        <!--实时字幕区域-->
        <LinearLayout
            android:id="@+id/real_time_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:visibility="gone"
            android:orientation="vertical"
            android:layout_marginTop="@dimen/dp22"
            android:layout_marginBottom="@dimen/dp63"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/wave_gradient_view"
            app:layout_constraintBottom_toTopOf="@id/middle_control_shadow_parent"
            >
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/real_time_view_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/select_language_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/real_time_view_margin"
                    android:layout_marginEnd="@dimen/real_time_view_margin"
                    android:minHeight="@dimen/dp28"
                    android:paddingStart="@dimen/dp9"
                    android:paddingTop="@dimen/dp6"
                    android:paddingEnd="@dimen/dp9"
                    android:paddingBottom="@dimen/dp6"
                    android:layout_marginBottom="@dimen/dp16"
                    android:background="@drawable/select_bg"
                    android:lines="1"
                    android:ellipsize="end"
                    style="@style/couiTextBodyXS"
                    android:gravity="center"
                    android:includeFontPadding="false"
                    android:drawablePadding="@dimen/dp4"
                    android:drawableStart="@drawable/ic_language_browser"
                    android:drawableEnd="@drawable/ic_polygon"
                    android:textColor="?attr/couiColorLabelPrimary"
                    android:fontFamily="sans-serif-medium"
                    android:textFontWeight="500"
                    android:forceDarkAllowed="false"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    />

                <ImageView
                    android:id="@+id/real_time_view_down"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:forceDarkAllowed="false"
                    android:layout_marginEnd="@dimen/real_time_view_margin"
                    android:background="@drawable/arror_drop_down_btn_drawable"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">
                </ImageView>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/captions_gradient_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:tag="CaptionsGradientView"
                app:backgroundWhole="@color/wave_recycler_background">

                <com.soundrecorder.record.views.CustomCOUIRecyclerView
                    android:id="@+id/captions_view"
                    android:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:paddingHorizontal="@dimen/real_time_view_margin"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:couiScrollbars="vertical"
                    tools:listitem="@layout/item_data"
                    android:tag="CaptionsRecyclerView" />

                <LinearLayout
                    android:id="@+id/captions_loading_layout"
                    android:visibility="gone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:layout_marginStart="@dimen/real_time_view_margin"
                    android:layout_marginEnd="@dimen/real_time_view_margin"
                    android:paddingStart="@dimen/real_time_item_data_padding"
                    android:paddingEnd="@dimen/real_time_item_data_padding"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/captions_text_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="@dimen/dp16"
                        android:textFontWeight="400"
                        android:textColor="@color/enable_recorde_button_text_color"
                        android:fontFamily="sans-serif-medium"
                        android:layout_gravity="center"
                        android:text="@string/subtitle_identify"/>

                    <com.oplus.anim.EffectiveAnimationView
                        android:id="@+id/captions_animation_view"
                        android:layout_width="@dimen/dp25"
                        android:layout_height="@dimen/dp20"
                        android:layout_gravity="center"
                        app:anim_repeatMode="reverse" />
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_start"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintGuide_begin="@dimen/responsive_ui_margin_large" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline_end"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintGuide_end="@dimen/responsive_ui_margin_large" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/middle_control_shadow_parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clipChildren="false"
            app:layout_constraintTop_toTopOf="@+id/middle_control"
            app:layout_constraintBottom_toBottomOf="@+id/middle_control"
            app:layout_constraintEnd_toEndOf="@id/middle_control"
            app:layout_constraintStart_toStartOf="@id/middle_control">
            <com.soundrecorder.common.view.ShadowViewDraw
                android:id="@+id/middle_control_shadow"
                android:layout_width="@dimen/circle_record_button_shadow"
                android:layout_height="@dimen/circle_record_button_shadow"
                app:shadowBlurRadius="@dimen/shadow_blur"
                app:shadowOffsetY="@dimen/shadow_offset_y"
                app:shadowColor="@color/edit_circle_btn_shadow"
                tools:ignore="MissingConstraints">
            </com.soundrecorder.common.view.ShadowViewDraw>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <FrameLayout
            android:id="@+id/middle_control"
            android:layout_width="@dimen/circle_record_button_diam"
            android:layout_height="@dimen/circle_record_button_diam"
            android:gravity="center"
            android:layout_marginBottom="@dimen/circle_record_button_margin_bottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/guideline_end"
            app:layout_constraintStart_toStartOf="@id/guideline_start">

            <com.soundrecorder.common.widget.AnimatedCircleButton
                android:id="@+id/recorder_save"
                android:layout_width="@dimen/circle_record_button_diam"
                android:layout_height="@dimen/circle_record_button_diam"
                android:contentDescription="@string/rename_save"
                android:src="@drawable/ic_record_stop_inside"
                android:forceDarkAllowed="false"
                android:tag="sharedRedCircleView"
                android:translationZ="@dimen/dp0"
                app:state_change="false"
                app:glow_enable="true"
                app:vibrate_toggle="true"
                app:reference_id="@id/recorder_save_reference"
                app:circle_gradient_start_color="@color/record_btn_gradient_start_color"
                app:circle_gradient_end_color="@color/record_btn_gradient_end_color"
                app:circle_color="?attr/couiColorContainerTheme"
                app:circle_radius="@dimen/circle_record_button_radius" />

            <ImageButton
                android:id="@+id/recorder_save_reference"
                android:forceDarkAllowed="false"
                android:layout_width="@dimen/dp56"
                android:layout_height="@dimen/dp56"
                android:background="@color/color_transparent"
                android:clickable="false"
                android:translationZ="@dimen/dp1"/>

        </FrameLayout>

        <LinearLayout
            android:id="@+id/left_mark_layout"
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:layout_marginEnd="@dimen/recorder_bottom_button_margin"
            android:tag="markView"
            app:layout_constraintBottom_toBottomOf="@id/middle_control"
            app:layout_constraintEnd_toStartOf="@id/middle_control"
            app:layout_constraintTop_toTopOf="@id/middle_control">

            <com.soundrecorder.record.views.RecorderAnimatedCircleButton
                android:id="@+id/left_mark_control"
                android:contentDescription="@string/talkback_flag"
                android:layout_width="@dimen/recorder_bottom_icon"
                android:layout_height="@dimen/recorder_bottom_icon"
                android:src="@drawable/ic_record_mark_state"
                android:forceDarkAllowed ="false"
                android:layout_gravity="center"
                app:circle_radius="@dimen/dp24"
                app:circle_color="@color/coui_color_container4"/>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ib_mark_photo_layout"
            android:layout_width="wrap_content"
            android:orientation="vertical"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/recorder_bottom_button_margin"
            android:tag="stopView"
            app:layout_constraintBottom_toBottomOf="@id/middle_control"
            app:layout_constraintStart_toEndOf="@id/middle_control"
            app:layout_constraintTop_toTopOf="@id/middle_control">

            <com.soundrecorder.record.views.RecorderAnimatedCircleButton
                android:id="@+id/red_circle_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:forceDarkAllowed="false"
                app:circle_radius="@dimen/dp24"
                android:src="@drawable/ic_pause"
                app:circle_color="@color/coui_color_container4" />
        </LinearLayout>

        <Space
            android:id="@+id/markListViewBottom"
            app:layout_constraintBottom_toTopOf="@id/transcription_tv"
            android:layout_width="match_parent"
            android:layout_height="@dimen/record_mark_list_margin_bottom" />

        <Space
            android:id="@+id/view_center_divider"
            android:layout_width="1px"
            android:layout_height="@dimen/dp60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/recorder_top"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:tag="timerView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginHorizontal="@dimen/common_time_area_margin_horizontal"
            app:layout_constraintTop_toBottomOf="@id/abl"
            android:layout_marginTop="@dimen/recorder_margin_top">

            <TextView
                android:id="@+id/timerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/recorder_timer_view_min_height"
                android:textSize="@dimen/sp48"
                android:lines="1"
                android:gravity="center"
                android:padding="0dp"
                android:textColor="@color/coui_color_primary_neutral"
                android:textAppearance="@style/couiTextAppearanceDisplayM"
                android:fontFamily="sans-serif-medium"
                android:fontFeatureSettings="tnum"
                android:textFontWeight="500"
                tools:text="00:11:22" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/transcription_box"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/middle_control"
            app:layout_constraintEnd_toEndOf="@id/middle_control"
            app:layout_constraintStart_toStartOf="@id/middle_control"
            android:layout_marginBottom="@dimen/transcription_box_margin_bottom">

            <TextView
                android:id="@+id/transcription_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/dp36"
                android:background="@drawable/recorder_translation_btn_bg"
                android:drawableStart="@drawable/ic_transcription_close"
                android:drawablePadding="@dimen/dp6"
                android:forceDarkAllowed="false"
                android:gravity="center"
                android:paddingStart="@dimen/dp16"
                android:paddingEnd="@dimen/dp16"
                android:maxLines="1"
                android:text="@string/turn_on_transcription"
                android:textFontWeight="500"
                android:textColor="@color/coui_color_label_primary"
                android:textSize="@dimen/dp14" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>