/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** File        : SubtitleScrollHelper.kt
 ** Description : SubtitleScrollHelper.kt
 ** Version     : 1.0
 ** Date        : 2025/08/13
 ** Author      : ********
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ********       2025/08/13      1.0      create
 ***********************************************************************/
package com.soundrecorder.record.subtitle

import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.record.R
import com.soundrecorder.record.RecordSubtitleAdapter
import com.soundrecorder.record.RecordSubtitleAdapter.Companion.TYPE_INTERMEDIATE
import com.soundrecorder.record.RecordSubtitleAdapter.Companion.TYPE_VAD_FINAL
import com.soundrecorder.record.RecordSubtitleAdapter.SpaceViewHolder
import com.soundrecorder.record.RecordSubtitleAdapter.ViewHolderData
import com.soundrecorder.record.subtitle.data.DisplaySubtitleEntry
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.math.absoluteValue

class SubtitleScrollHelper {

    private var subtitleRecyclerView: COUIRecyclerView? = null
    private var recordSubtitleAdapter: RecordSubtitleAdapter? = null
    private var persistentZoneBoundary = 0
    private var spaceViewHeight = 0
    private var defaultSpaceViewHeight = 0
    private val isNeedUpdateData = AtomicBoolean(false)
    private val isUserScrolling = AtomicBoolean(false)
    private val isAutoScrollBottom = AtomicBoolean(false)
    private var lasDataItemId = ""
    private var measureItemView: View? = null
    private var resetRunnable: Runnable? = null
    private val scrollListener = ScrollListener()
    private val layoutChangeListener = LayoutChangeListener()
    private val touchListener = TouchListener()
    private val handler = Handler(Looper.getMainLooper())
    private val diffCallback = SubtitleDiffCallback(listOf(), listOf())
    private val oldDataList = mutableListOf<DisplaySubtitleEntry>()


    companion object {
        private const val TAG = "SubtitleScrollHelper"
        private const val BUFFER_ZONE_RATIO: Float = 0.05f
        private const val PERSISTENT_ZONE_RATIO: Float = 0.95f
        private const val RESET_DELAY = 5000L
        private const val SCROLL_BASE_DURATION = 100      // 基准时长(毫秒)
        private const val SCROLL_BASE_DISTANCE = 100      // 基准距离(像素)
        private const val SCROLL_MIN_DURATION = 100       // 最短滚动时长(毫秒)
        private const val SCROLL_MAX_DURATION = 1000      // 最长滚动时长(毫秒)
    }

    fun attached(recyclerView: COUIRecyclerView, adapter: RecordSubtitleAdapter) {
        recordSubtitleAdapter = adapter
        subtitleRecyclerView = recyclerView
        subtitleRecyclerView?.apply {
            addOnLayoutChangeListener(layoutChangeListener)
            addOnScrollListener(scrollListener)
            setOnTouchListener(touchListener)
            itemAnimator = null
            post {
                updateParameter()
            }
        }
    }

    fun detached() {
        subtitleRecyclerView?.let {
            it.removeOnScrollListener(scrollListener)
            it.removeOnLayoutChangeListener(layoutChangeListener)
            it.setOnTouchListener(null)
        }
        subtitleRecyclerView = null
        recordSubtitleAdapter = null
        measureItemView = null
        resetRunnable?.let { handler.removeCallbacks(it) }
        resetRunnable = null
    }

    inner class ScrollListener : RecyclerView.OnScrollListener() {
        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            super.onScrollStateChanged(recyclerView, newState)
            when (newState) {
                RecyclerView.SCROLL_STATE_IDLE -> {
                    if (isNeedUpdateData.get()) {
                        refreshData()
                        isNeedUpdateData.set(false)
                    }
                    if (isAutoScrollBottom.get()) {
                        updateSpaceHeight(spaceViewHeight)
                        refreshData(true)
                        isUserScrolling.set(false)
                        isAutoScrollBottom.set(false)
                    }
                    DebugUtil.i(TAG, "onScrollStateChanged SCROLL_STATE_IDLE")
                }
            }
        }
    }

    inner class LayoutChangeListener : View.OnLayoutChangeListener {
        override fun onLayoutChange(
            v: View?,
            left: Int,
            top: Int,
            right: Int,
            bottom: Int,
            oldLeft: Int,
            oldTop: Int,
            oldRight: Int,
            oldBottom: Int
        ) {
            if (right - left != oldRight - oldLeft || bottom - top != oldBottom - oldTop) {
                updateParameter()
            }
        }
    }

    inner class TouchListener : View.OnTouchListener {
        override fun onTouch(v: View?, event: MotionEvent?): Boolean {
            when (event?.action) {
                MotionEvent.ACTION_DOWN, MotionEvent.ACTION_MOVE -> {
                    resetRunnable?.let { handler.removeCallbacks(it) }
                    updateSpaceHeight(defaultSpaceViewHeight)
                    if (isUserScrolling.get().not()) {
                        subtitleRecyclerView?.stopScroll()
                        isUserScrolling.set(true)
                    }
                    if (isAutoScrollBottom.get()) {
                        isAutoScrollBottom.set(false)
                        isUserScrolling.set(true)
                        subtitleRecyclerView?.stopScroll()
                    }
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    resetRunnable = Runnable {
                        val layoutManager = subtitleRecyclerView?.layoutManager as? LinearLayoutManager
                        val dataList = recordSubtitleAdapter?.dataList
                        val position = if (dataList.isNullOrEmpty()) {
                            -1
                        } else {
                            dataList.size - 1
                        }
                        val firstVisiblePos = layoutManager?.findFirstCompletelyVisibleItemPosition()
                        val lastVisiblePos = layoutManager?.findLastCompletelyVisibleItemPosition()
                        // 最后一个数据项Item在可见范围内
                        if (firstVisiblePos != null && lastVisiblePos != null && position in firstVisiblePos..lastVisiblePos) {
                            updateSpaceHeight(spaceViewHeight)
                            isUserScrolling.set(false)
                        } else {
                            // 待滚动完成后重置标志位和Space的高度
                            isAutoScrollBottom.set(true)
                            subtitleRecyclerView?.smoothScrollToPosition(position)
                        }
                    }
                    resetRunnable?.let { handler.postDelayed(it, RESET_DELAY) }
                }
            }
            return false
        }
    }

    private fun updateParameter() {
        subtitleRecyclerView?.let {
            persistentZoneBoundary = (it.height * PERSISTENT_ZONE_RATIO).toInt()
            spaceViewHeight = it.height
            defaultSpaceViewHeight = (it.height * BUFFER_ZONE_RATIO).toInt()
            updateSpaceHeight(spaceViewHeight)
        }
    }

    private fun updateSpaceHeight(newHeightValue: Int) {
        val dataList = recordSubtitleAdapter?.dataList ?: return
        val spaceViewHolder = subtitleRecyclerView?.findViewHolderForAdapterPosition(dataList.size) as? SpaceViewHolder ?: return
        if (spaceViewHolder.itemView.height == newHeightValue) return
        val layoutParams = spaceViewHolder.itemView.layoutParams
        layoutParams.height = newHeightValue
        spaceViewHolder.itemView.layoutParams = layoutParams
    }

    private fun calculateItemHeightByData(data: DisplaySubtitleEntry): Int {
        val context = subtitleRecyclerView?.context ?: return 0
        if (measureItemView == null) {
            measureItemView = LayoutInflater.from(context).inflate(R.layout.item_data, subtitleRecyclerView, false)
        }
        var measuredHeight = 0
        measureItemView?.let { itemView ->
            val viewHolder = ViewHolderData(itemView)
            viewHolder.updateData(data, 0, null)
            subtitleRecyclerView?.let {
                val availableWidth = it.width - it.paddingStart - it.paddingEnd
                itemView.measure(
                    View.MeasureSpec.makeMeasureSpec(availableWidth, View.MeasureSpec.EXACTLY),
                    View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
                )
            }
            measuredHeight = itemView.measuredHeight
        }
        DebugUtil.d(TAG, "calculateItemHeightByData measuredHeight=$measuredHeight")
        return measuredHeight
    }

    private fun calculateNewDataHeight(lasDataItemId: String): Int {
        val dataList = recordSubtitleAdapter?.dataList ?: return 0
        var totalHeight = 0
        for (i in dataList.size - 1 downTo 0) {
            val item = dataList[i]
            if (item.originContent.id != lasDataItemId) {
                if (item.originContent.vadType == TYPE_INTERMEDIATE || item.originContent.vadType == TYPE_VAD_FINAL) {
                    totalHeight += calculateItemHeightByData(item)
                }
            } else {
                totalHeight += calculateItemHeightByData(item)
                break
            }
        }
        DebugUtil.d(TAG, "calculateNewDataHeight totalHeight=$totalHeight")
        return totalHeight
    }

    /**
     * 获取最后一个数据项对应的Item顶部位置
     */
    private fun getLasDataItemTopPosition(lasDataItemId: String): Int {
        if (lasDataItemId.isEmpty()) return -1
        val dataList = recordSubtitleAdapter?.dataList ?: return -1
        var dataIndex = -1
        for (i in dataList.lastIndex downTo 0) {
            if (dataList[i].originContent.id == lasDataItemId) {
                dataIndex = i
                break
            }
        }
        if (dataIndex == -1) return -1
        val layoutManager = subtitleRecyclerView?.layoutManager as? LinearLayoutManager ?: return -1
        val itemView = layoutManager.findViewByPosition(dataIndex) ?: return -1
        val top = itemView.top
        DebugUtil.d(TAG, "getLasDataItemTopPosition top=$top")
        return top
    }

    private fun updateLastDataItemId() {
        val dataList = recordSubtitleAdapter?.dataList ?: return
        for (i in dataList.lastIndex downTo 0) {
            val item = dataList[i]
            if (item.originContent.vadType == TYPE_INTERMEDIATE || item.originContent.vadType == TYPE_VAD_FINAL) {
                lasDataItemId = item.originContent.id
                break
            }
        }
        DebugUtil.d(TAG, "updateLastDataItemId lasDataItemId=$lasDataItemId")
    }

    private fun refreshData(isFull: Boolean = false) {
        recordSubtitleAdapter?.let {
            if (isFull) {
                it.notifyDataSetChanged()
            }  else {
                diffCallback.newList = it.dataList
                diffCallback.oldList = oldDataList
                DiffUtil.calculateDiff(diffCallback).dispatchUpdatesTo(it)
            }
            oldDataList.clear()
            oldDataList.addAll(it.dataList.deepCopy())
        }
    }

    private fun List<DisplaySubtitleEntry>.deepCopy(): List<DisplaySubtitleEntry> {
        return this.map { entry ->
            entry.copy(
                originContent = entry.originContent.copy(),
                insertedMarks = entry.insertedMarks.toMutableList()
            )
        }
    }

    private fun calculateScrollDurationByRatio(distance: Int): Int {
        // 基于比例计算滚动时长: 时长 = |距离| × 基准时长 / 基准距离
        val duration = (distance.absoluteValue * SCROLL_BASE_DURATION / SCROLL_BASE_DISTANCE)
        return duration.coerceAtLeast(SCROLL_MIN_DURATION).coerceAtMost(SCROLL_MAX_DURATION)
    }

    fun getSpaceHeight(): Int {
        return if (isUserScrolling.get()) {
            defaultSpaceViewHeight
        } else {
            spaceViewHeight
        }
    }

    fun handleDataUpdate() {
        if (isUserScrolling.get()) {
            refreshData()
            updateLastDataItemId()
            return
        }

        // 如果正在动画滚动，则不更新数据，待滚动完毕后更新
        if (isNeedUpdateData.get() || isAutoScrollBottom.get()) {
            updateLastDataItemId()
            return
        }

        // 获取参照的顶部坐标
        val referenceTop = getLasDataItemTopPosition(lasDataItemId)
        if (referenceTop == -1) {
            refreshData()
            updateLastDataItemId()
            return
        }

        // 计算即将要更新的数据在界面的高度
        val totalHeight = calculateNewDataHeight(lasDataItemId)

        // 计算新数据在的底部位置
        val newDataBottom = referenceTop + totalHeight

        // 判断是否需要滚动
        if (newDataBottom > persistentZoneBoundary) {
            isNeedUpdateData.set(true)
            val needScrollDistance = newDataBottom - persistentZoneBoundary
            DebugUtil.d(TAG, "needScrollDistance=$needScrollDistance")
            subtitleRecyclerView?.smoothScrollBy(0, needScrollDistance, null, calculateScrollDurationByRatio(needScrollDistance))
        } else {
            refreshData()
        }

        updateLastDataItemId()
    }
}