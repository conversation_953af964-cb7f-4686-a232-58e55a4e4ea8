/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** File        : SubtitleDiffCallback.kt
 ** Description : SubtitleDiffCallback.kt
 ** Version     : 1.0
 ** Date        : 2025/08/12
 ** Author      : ********
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ********       2025/08/12      1.0      create
 ***********************************************************************/
package com.soundrecorder.record.subtitle

import androidx.recyclerview.widget.DiffUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.record.subtitle.data.DisplayMark
import com.soundrecorder.record.subtitle.data.DisplaySubtitleEntry

class SubtitleDiffCallback(var oldList: List<DisplaySubtitleEntry>, var newList: List<DisplaySubtitleEntry>) : DiffUtil.Callback() {

    override fun getOldListSize(): Int {
        return oldList.size
    }

    override fun getNewListSize(): Int {
        return newList.size
    }

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        val oldItem = oldList.getOrNull(oldItemPosition) ?: return false
        val newItem = newList.getOrNull(newItemPosition) ?: return false
        return oldItem.originContent.id == newItem.originContent.id
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        val oldItem = oldList.getOrNull(oldItemPosition) ?: return false
        val newItem = newList.getOrNull(newItemPosition) ?: return false
        return areFieldsSameExceptOrigin(oldItem, newItem) && areConvertContentItemsTheSame(oldItem.originContent, newItem.originContent)
    }

    /**
     * 比较两个DisplaySubtitleEntry中除了originContent之外的所有字段
     */
    fun areFieldsSameExceptOrigin(oldItem: DisplaySubtitleEntry, newItem: DisplaySubtitleEntry): Boolean {
        val displayContentSame = oldItem.displayContent == newItem.displayContent
        val isFromCompletedSubtitlesSame = oldItem.isFromCompletedSubtitles == newItem.isFromCompletedSubtitles
        val lastContentSame = oldItem.lastContent == newItem.lastContent
        val insertedMarksSame = areInsertedMarksTheSame(oldItem.insertedMarks, newItem.insertedMarks)
        return displayContentSame && isFromCompletedSubtitlesSame && lastContentSame && insertedMarksSame
    }

    /**
     * 比较两个插入标记列表是否相同
     */
    private fun areInsertedMarksTheSame(oldMarks: List<DisplayMark>, newMarks: List<DisplayMark>): Boolean {
        if (oldMarks.size != newMarks.size) return false

        for (i in oldMarks.indices) {
            val oldMark = oldMarks[i]
            val newMark = newMarks[i]
            if (oldMark.timeOffset != newMark.timeOffset ||
                oldMark.insertPosition != newMark.insertPosition
            ) {
                return false
            }
        }
        return true
    }

    /**
     * 比较两个ConvertContentItem的内容是否相同
     * 用于DiffUtil的areContentsTheSame方法
     */
    fun areConvertContentItemsTheSame(oldItem: ConvertContentItem, newItem: ConvertContentItem): Boolean {
        return oldItem.startTime == newItem.startTime &&
                oldItem.endTime == newItem.endTime &&
                oldItem.textContent == newItem.textContent &&
                oldItem.roleId == newItem.roleId &&
                oldItem.roleName == newItem.roleName &&
                oldItem.vadType == newItem.vadType &&
                oldItem.textOptimizationState == newItem.textOptimizationState &&
                oldItem.asrLanguage == newItem.asrLanguage
    }
}