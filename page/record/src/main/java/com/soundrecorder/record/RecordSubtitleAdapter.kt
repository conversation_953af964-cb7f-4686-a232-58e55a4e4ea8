/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - RecordSubtitleAdapter.kt
 * Description:
 *     The helper class for ui showing subtitle in record pat.
 *
 * Version: 1.0
 * Date: 2025-05-30
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2025-05-30   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.record

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.Space
import android.widget.TextView
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.graphics.ColorUtils
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.oplus.anim.EffectiveAnimationView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.NightModeUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.utils.initPressFeedback
import com.soundrecorder.common.widget.COUIAnimateTextView
import com.soundrecorder.record.subtitle.SubtitleScrollHelper
import com.soundrecorder.record.subtitle.data.DisplayMark
import com.soundrecorder.record.subtitle.data.DisplaySubtitleEntry
import com.soundrecorder.recorderservice.api.RecorderViewModelApi
import java.lang.ref.WeakReference
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.floor

class RecordSubtitleAdapter(var context: Context) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    var dataList = mutableListOf<DisplaySubtitleEntry>()
    private var listener: OnItemClickListener? = null
    private var oldDataSize = 0
    private var holderDataPosition = -1
    private var oldDataRoleId = -1
    private var speakerColors = context.resources.getIntArray(com.soundrecorder.common.R.array.speakerColor)
    private var speakerStyleHashMap = ConcurrentHashMap<Int, Int>()
    var languageCode = RecorderViewModelApi.getCurSelectedLanguage()
    private var subtitleScrollHelper: SubtitleScrollHelper? = null
    private val animatedDataIds = mutableSetOf<String>()
    private val toBeAnimateDataIds = mutableSetOf<String>()
    val defaultColor = context.getColor(com.support.appcompat.R.color.coui_color_label_primary)
    val optimizedColor = context.getColor(com.support.appcompat.R.color.coui_color_label_secondary)

    companion object {
        const val TYPE_DATA_OK = 1
        const val TYPE_DATA_NOW = 2
        const val TYPE_NO_NET = 3
        const val TYPE_NO_SERVICE = 4
        const val TYPE_SPACE_VIEW = 5
        const val TYPE_NO_DATA = -1
        private const val TIME_ZERO = 0
        private const val TIME_HOUR_24 = 24
        private const val TIME_HOUR = 3600000
        const val TAG = "RecordSubtitleAdapter"
        const val TYPE_VAD_FINAL = "VAD_FINAL"
        const val TYPE_INTERMEDIATE = "INTERMEDIATE"
        const val TYPE_NOT_NET = "TYPE_NO_NET"
        const val TYPE_NOT_SERVICE = "TYPE_NO_SERVICE"
        private val TIME_FORMATTER_HH_MM_SS = DateTimeFormatter.ofPattern("HH:mm:ss")
        private val TIME_FORMATTER_00_MM_SS = DateTimeFormatter.ofPattern("00:mm:ss")
        private const val INVALID_TIME_FORMAT_LONG = "--:--:--"
        private const val INVALID_TIME_FORMAT_SHORT = "--:--"
        private const val ALPHA_ANIMATION_DURATION = 350L
        private const val SPEAKER_BACKGROUND_ALPHA = 255 * 0.1
        private const val SPEAKER_BACKGROUND_ALPHA_NIGHT = 255 * 0.2
        private const val SPACING_MULTI = 1.42f
        private const val COLOR_MAX_COUNT = 10
        private const val TEXT_COLOR_ANIMATION = 100L

        @JvmStatic
        private fun formatTime(time: Long): String {
            return try {
                val duration = java.time.Duration.ofMillis(time)
                if (duration.toHours() > TIME_ZERO) {
                    TIME_FORMATTER_HH_MM_SS.format(
                        Instant.ofEpochMilli(time).atZone(ZoneId.of("UTC"))
                            .withHour(duration.toHours().toInt() % TIME_HOUR_24)
                    )
                } else {
                    TIME_FORMATTER_00_MM_SS.format(Instant.ofEpochMilli(time).atZone(ZoneId.of("UTC")))
                }
            } catch (e: IllegalArgumentException) {
                DebugUtil.e(TAG, "Invalid timestamp format: ${e.message}")
                if (time >= TIME_HOUR) INVALID_TIME_FORMAT_LONG else INVALID_TIME_FORMAT_SHORT
            } catch (e: ArithmeticException) {
                DebugUtil.e(TAG, "Timestamp calculation error: ${e.message}")
                if (time >= TIME_HOUR) INVALID_TIME_FORMAT_LONG else INVALID_TIME_FORMAT_SHORT
            } catch (e: UnsupportedOperationException) {
                DebugUtil.e(TAG, "Unsupported date operation: ${e.message}")
                if (time >= TIME_HOUR) INVALID_TIME_FORMAT_LONG else INVALID_TIME_FORMAT_SHORT
            }
        }
    }

    class ViewHolderData(itemView: View, adapter: RecordSubtitleAdapter? = null) : RecyclerView.ViewHolder(itemView) {
        val speaker: TextView = itemView.findViewById(R.id.tv_speaker)
        val speakerPortrait: ImageView = itemView.findViewById(R.id.speaker_portrait)
        val dataSpeaker: ConstraintLayout = itemView.findViewById(R.id.data_speaker)
        val llSpeaker: LinearLayout = itemView.findViewById(R.id.ll_speaker)
        val startTime: TextView = itemView.findViewById(R.id.start_time)
        val subtitle: TextView = itemView.findViewById(R.id.item_content)
        val speakerBackgroundAnimation: EffectiveAnimationView  =
            itemView.findViewById<EffectiveAnimationView?>(R.id.speaker_background_animation).apply {
                setAnimation(com.soundrecorder.common.R.raw.speaker_background_loading)
                repeatCount = ValueAnimator.INFINITE
        }
        val speakerPortraitAnimation: EffectiveAnimationView =
            itemView.findViewById<EffectiveAnimationView?>(R.id.speaker_portrait_animation).apply {
                setAnimation(com.soundrecorder.common.R.raw.speaker_portrait_loading)
                repeatCount = ValueAnimator.INFINITE
        }
        val llSpeakerAnimation: LinearLayout  = itemView.findViewById(R.id.ll_speaker_animation)
        val tvSpeakerAnimation: TextView  = itemView.findViewById(R.id.tv_speaker_animation)
        val weakFefAdapter = WeakReference(adapter)

        init {
            subtitle.gravity = if (itemView.layoutDirection == View.LAYOUT_DIRECTION_RTL) {
                Gravity.RIGHT
            } else {
                Gravity.LEFT
            }
            llSpeaker.initPressFeedback()
            subtitle.setLineSpacing(0f, SPACING_MULTI)
        }

        @SuppressLint("ResourceAsColor")
        fun setSpeakerStyle(backgroundColor: Int) {
            if (backgroundColor != -1) {
                val portraitDrawable =
                    itemView.context.resources.getDrawable(R.drawable.speaker_portrait_picture)
                portraitDrawable.setTint(backgroundColor)
                speakerPortrait.background = portraitDrawable
                val backgroundDrawable =
                    itemView.context.resources.getDrawable(R.drawable.background_speaker)
                val alpha = if (NightModeUtil.isNightMode(itemView.context)) {
                    SPEAKER_BACKGROUND_ALPHA_NIGHT
                } else {
                    SPEAKER_BACKGROUND_ALPHA
                }
                val transparentColor = ColorUtils.setAlphaComponent(backgroundColor, floor(alpha).toInt())
                backgroundDrawable.setTint(transparentColor)
                llSpeaker.background = backgroundDrawable
            }
        }

        fun updateData(data: DisplaySubtitleEntry, position: Int, listener: OnItemClickListener?) {
            speaker.text = data.originContent.roleName
            if (llSpeaker.isLaidOut && speakerBackgroundAnimation.isLaidOut) {
                speakerBackgroundAnimation.apply {
                    pivotX = 0f
                    scaleX = llSpeakerAnimation.width.div(speakerBackgroundAnimation.width.toFloat())
                    scaleY = llSpeakerAnimation.height.div(speakerBackgroundAnimation.height.toFloat())
                }
            }
            DebugUtil.e(TAG, "Unsupported speaker: ${speaker.text}")
            if (data.originContent.roleId == -1
                || data.originContent.roleName == null
            ) {
                dataSpeaker.visibility = View.INVISIBLE
            }
            llSpeaker.setOnClickListener {
                listener?.onItemClickSpeaker(position)
            }
            startTime.text = formatTime(data.originContent.startTime)
            updateTextColor(data)
            subtitle.text = if (data.displayContent.isNotEmpty()) {
                data.displayContent
            } else {
                data.originContent.textContent
            }
        }

        private fun buildAlphaAnimation(view: View, startValue: Float, endValue: Float): ObjectAnimator {
            return ObjectAnimator.ofFloat(view, "alpha", startValue, endValue).apply {
                duration = ALPHA_ANIMATION_DURATION
                interpolator = LinearInterpolator()
                start()
            }
        }

        fun speakerRecognitionFinish(execAnimation: Boolean = false) {
            //讲话人确定，json动画淡出，讲话人淡入
            llSpeaker.visibility = View.VISIBLE
            if (execAnimation) {
                llSpeaker.alpha = 0f
                tvSpeakerAnimation.text = itemView.resources.getString(com.soundrecorder.base.R.string.recognizing_speaker)
                buildAlphaAnimation(llSpeaker, 0f, 1f)
                // 2. JSON动画淡出
                speakerBackgroundAnimation.visibility = View.VISIBLE
                llSpeakerAnimation.visibility = View.VISIBLE
                speakerBackgroundAnimation.alpha = 1f
                llSpeakerAnimation.alpha = 1f
                speakerBackgroundAnimation.playAnimation()
                speakerPortraitAnimation.playAnimation()
                buildAlphaAnimation(speakerBackgroundAnimation, 1f, 0f).addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        speakerBackgroundAnimation.cancelAnimation()
                        speakerBackgroundAnimation.visibility = View.INVISIBLE
                    }
                })
                buildAlphaAnimation(llSpeakerAnimation, 1f, 0f).addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        speakerPortraitAnimation.cancelAnimation()
                        llSpeakerAnimation.visibility = View.INVISIBLE
                    }
                })
            } else {
                speakerBackgroundAnimation.visibility = View.INVISIBLE
                llSpeakerAnimation.visibility = View.INVISIBLE
            }
        }

        private fun updateTextColor(data: DisplaySubtitleEntry) {
            val adapter = weakFefAdapter.get() ?: return
            val dataId = data.originContent.id
            val defaultColor = adapter.defaultColor
            val optimizedColor = adapter.optimizedColor

            when (data.originContent.textOptimizationState) {
                ConvertContentItem.TextOptimizationState.UNPROCESSED -> subtitle.setTextColor(defaultColor)
                ConvertContentItem.TextOptimizationState.PROCESSING -> {
                    subtitle.setTextColor(defaultColor)
                    adapter.toBeAnimateDataIds.add(dataId)
                }
                ConvertContentItem.TextOptimizationState.COMPLETED -> {
                    if (adapter.animatedDataIds.contains(dataId)) {
                        subtitle.setTextColor(optimizedColor)
                    } else {
                        animateAndSaveStatus(defaultColor, optimizedColor)
                        adapter.toBeAnimateDataIds.remove(dataId)
                        adapter.animatedDataIds.add(dataId)
                    }
                }
            }
        }

        private fun animateAndSaveStatus(startColor: Int, endColor: Int) {
            val startAlpha = Color.alpha(startColor)
            val endAlpha = Color.alpha(endColor)
            val red = Color.red(startColor)
            val green = Color.green(startColor)
            val blue = Color.blue(startColor)
            val animator = ValueAnimator.ofInt(startAlpha, endAlpha)
            animator.duration = TEXT_COLOR_ANIMATION
            animator.addUpdateListener { animation ->
                val alpha = animation.animatedValue as Int
                val newColor = Color.argb(alpha, red, green, blue)
                subtitle.setTextColor(newColor)
            }
            animator.start()
        }
    }

    class ViewHolderDataNow(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val speaker: TextView = itemView.findViewById(R.id.tv_speaker)
        val tvSpeakerAnimation: TextView = itemView.findViewById(R.id.tv_speaker_animation)
        private val llSpeaker: LinearLayout = itemView.findViewById(R.id.ll_speaker)
        private val llSpeakerAnimation: LinearLayout = itemView.findViewById(R.id.ll_speaker_animation)
        private val speakerPortrait: EffectiveAnimationView =
            itemView.findViewById<EffectiveAnimationView?>(R.id.speaker_portrait_animation).apply {
                setAnimation(com.soundrecorder.common.R.raw.speaker_portrait_loading)
                repeatCount = ValueAnimator.INFINITE
        }
        private val speakerBackgroundAnimation: EffectiveAnimationView =
            itemView.findViewById<EffectiveAnimationView?>(R.id.speaker_background_animation).apply {
                setAnimation(com.soundrecorder.common.R.raw.speaker_background_loading)
                repeatCount = ValueAnimator.INFINITE
        }
        private val dataSpeaker: ConstraintLayout = itemView.findViewById(R.id.data_speaker)
        private val startTime: TextView = itemView.findViewById(R.id.start_time)
        private val subtitle: COUIAnimateTextView = itemView.findViewById(R.id.item_content)

        init {
            subtitle.gravity = if (itemView.layoutDirection == View.LAYOUT_DIRECTION_RTL) {
                Gravity.RIGHT
            } else {
                Gravity.LEFT
            }
            subtitle.setLineSpacing(0f, SPACING_MULTI)
            llSpeaker.initPressFeedback()
        }


        /**
         * 启动当前讲话人的JSON动效（第一次出现）
         */
        fun startSpeakingAnimation(isFirst: Boolean) {
            if (speakerBackgroundAnimation.isAnimating && speakerPortrait.isAnimating) return
            if (isFirst) {
                speakerBackgroundAnimation.alpha = 0f
                llSpeakerAnimation.alpha = 0f
                buildAlphaAnimation(speakerBackgroundAnimation, 0f, 1f)
                buildAlphaAnimation(llSpeakerAnimation, 0f, 1f)
            }
            speakerBackgroundAnimation.visibility = View.VISIBLE
            llSpeaker.visibility = View.INVISIBLE
            llSpeakerAnimation.visibility = View.VISIBLE
            speakerBackgroundAnimation.playAnimation()
            speakerPortrait.playAnimation()
        }

        @SuppressLint("ResourceAsColor")
        fun setSpeakerStyle(backgroundColor: Int) {
            if (backgroundColor != -1) {
                val portraitDrawable =
                    itemView.context.resources.getDrawable(R.drawable.speaker_portrait_picture)
                portraitDrawable.setTint(backgroundColor)
                speakerPortrait.background = portraitDrawable
                val backgroundDrawable =
                    itemView.context.resources.getDrawable(R.drawable.background_speaker)
                // 设置背景颜色为肖像颜色的0.15透明度，使背景更明显
                val alpha = if (NightModeUtil.isNightMode(itemView.context)) {
                    SPEAKER_BACKGROUND_ALPHA_NIGHT
                } else {
                    SPEAKER_BACKGROUND_ALPHA
                }
                val transparentColor = ColorUtils.setAlphaComponent(backgroundColor, floor(alpha).toInt())
                backgroundDrawable.setTint(transparentColor)
                llSpeaker.background = backgroundDrawable
            }
        }

        fun speakerRecognitionFinish(execAnimation: Boolean = false) {
            //讲话人确定，json动画淡出，讲话人淡入
            llSpeaker.visibility = View.VISIBLE
            if (execAnimation) {
                llSpeaker.alpha = 0f
                buildAlphaAnimation(llSpeaker, 0f, 1f)
                // 2. JSON动画淡出
                speakerBackgroundAnimation.visibility = View.VISIBLE
                llSpeakerAnimation.visibility = View.VISIBLE
                speakerBackgroundAnimation.alpha = 1f
                llSpeakerAnimation.alpha = 1f
                speakerBackgroundAnimation.playAnimation()
                speakerPortrait.playAnimation()
                buildAlphaAnimation(speakerBackgroundAnimation, 1f, 0f).addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        speakerBackgroundAnimation.visibility = View.INVISIBLE
                    }
                })
                buildAlphaAnimation(llSpeakerAnimation, 1f, 0f).addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        llSpeakerAnimation.visibility = View.INVISIBLE
                    }
                })
            } else {
                speakerBackgroundAnimation.visibility = View.INVISIBLE
                llSpeakerAnimation.visibility = View.INVISIBLE
            }
        }

        private fun buildAlphaAnimation(view: View, startValue: Float, endValue: Float): ObjectAnimator {
            return ObjectAnimator.ofFloat(view, "alpha", startValue, endValue).apply {
                duration = ALPHA_ANIMATION_DURATION
                interpolator = LinearInterpolator()
                start()
            }
        }

        /**
         * 更新数据
         * @param data 数据
         * @param position 位置
         * @param languageCode 语言代码
         * @param isNewParagraph 是否新的段落
         * @param listener 监听器
         */
        fun updateData(
            data: DisplaySubtitleEntry,
            position: Int,
            languageCode: String,
            isNewParagraph: Boolean,
            listener: OnItemClickListener?
        ) {
            speaker.text = data.originContent.roleName
            tvSpeakerAnimation.text = itemView.resources.getString(com.soundrecorder.base.R.string.recognizing_speaker)
            if (llSpeakerAnimation.isLaidOut && speakerBackgroundAnimation.isLaidOut) {
                speakerBackgroundAnimation.apply {
                    pivotX = 0f
                    scaleX = llSpeakerAnimation.width.div(speakerBackgroundAnimation.width.toFloat())
                    scaleY = llSpeakerAnimation.height.div(speakerBackgroundAnimation.height.toFloat())
                }
            }
            DebugUtil.e(TAG, "Unsupported speaker: ${speaker.text}")
            if (data.originContent.roleId == -1
                || data.originContent.roleName == null
            ) {
                dataSpeaker.visibility = View.INVISIBLE
            }
            llSpeaker.setOnClickListener {
                if (speaker.visibility == View.VISIBLE) {
                    listener?.onItemClickSpeaker(position)
                }
            }
            startTime.text = formatTime(data.originContent.startTime)
            val content = if (data.displayContent.isNotEmpty()) {
                data.displayContent
            } else {
                data.originContent.textContent
            }
            subtitle.setSubtitleAnimateText(content, languageCode, isNewParagraph)
        }
    }

    class ViewHolderError(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val netError: TextView = itemView.findViewById(R.id.tv_error)
        val retryTv: TextView = itemView.findViewById(R.id.tv_retry)
        fun updateData(context: Context, data: DisplaySubtitleEntry) {
            if ((data.originContent.vadType) == TYPE_NOT_NET) {
                netError.text = context.resources.getString(com.soundrecorder.base.R.string.subtitle_network_connect_error)
                retryTv.text = context.resources.getString(com.soundrecorder.base.R.string.retry)
                COUITextViewCompatUtil.setPressRippleDrawable(retryTv)
                retryTv.setOnClickListener {
                    if (NetworkUtils.isNetworkInvalid(context)) {
                        Toast.makeText(context, com.soundrecorder.common.R.string.network_disconnect, Toast.LENGTH_SHORT).show()
                        return@setOnClickListener
                    }
                    DebugUtil.i(TAG, "ViewHolderError-RecorderViewModelApi.externalInitAsr")
                    RecorderViewModelApi.externalInitAsr()
                }
            } else if (data.originContent.vadType == TYPE_NOT_SERVICE) {
                netError.text = context.resources.getString(com.soundrecorder.base.R.string.subtitle_service_connect_error)
                retryTv.text = context.resources.getString(com.soundrecorder.base.R.string.retry)
                COUITextViewCompatUtil.setPressRippleDrawable(retryTv)
                retryTv.setOnClickListener {
                    if (NetworkUtils.isNetworkInvalid(context)) {
                        Toast.makeText(context, com.soundrecorder.common.R.string.network_disconnect, Toast.LENGTH_SHORT).show()
                        return@setOnClickListener
                    }
                    RecorderViewModelApi.externalInitAsr()
                }
            }
        }
    }

    /**
     * 获取类型
     */
    override fun getItemViewType(position: Int): Int {
        if (position == dataList.size) {
            return TYPE_SPACE_VIEW
        }
        val item = dataList.getOrNull(position) ?: return TYPE_DATA_OK
        return when (item.originContent.vadType) {
            TYPE_VAD_FINAL -> TYPE_DATA_OK
            TYPE_INTERMEDIATE -> TYPE_DATA_NOW
            TYPE_NOT_NET -> TYPE_NO_NET
            TYPE_NOT_SERVICE -> TYPE_NO_SERVICE
            else -> TYPE_NO_DATA
        }
    }

    /**
     * 创建 ViewHolder
     */
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_DATA_OK -> {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_data, parent, false)
                ViewHolderData(item, this)
            }

            TYPE_DATA_NOW -> {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_data_now, parent, false)
                ViewHolderDataNow(item)
            }

            TYPE_NO_NET -> {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_error, parent, false)
                ViewHolderError(item)
            }

            TYPE_NO_SERVICE -> {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_error, parent, false)
                ViewHolderError(item)
            }

            TYPE_SPACE_VIEW -> {
                val spaceView = Space(parent.context)
                val height = subtitleScrollHelper?.getSpaceHeight() ?: 0
                spaceView.layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, height)
                SpaceViewHolder(spaceView)
            }

            else -> {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_data, parent, false)
                ViewHolderData(item, this)
            }
        }
    }

    /**
     * 绑定数据到 ViewHolder
     */
    @SuppressLint("ClickableViewAccessibility")
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (dataList.size <= 0) return
        if (position >= dataList.size && holder is SpaceViewHolder) {
            val height = subtitleScrollHelper?.getSpaceHeight() ?: 0
            holder.itemView.layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, height)
            return
        }
        val data = dataList[position]
        val speakerStyle = calculateSpeakerStyle(holder, data)
        val nowDataSize = dataList.size
        if (holder is ViewHolderData) {
            if (speakerStyle != -1) {
                holder.setSpeakerStyle(speakerStyle)
            }
            holder.updateData(data, position, listener)
            val execAnimation = position == holderDataPosition
            if (execAnimation) {
                holderDataPosition = -1
            }
            holder.speakerRecognitionFinish(execAnimation)
        } else if (holder is ViewHolderDataNow) {
            var isNewParagraph = false
            if (oldDataSize != nowDataSize) {
                oldDataSize = nowDataSize
                isNewParagraph = true
                holderDataPosition = position
            }
            holder.updateData(data, position, languageCode, isNewParagraph, listener)
            // 根据不同情况执行不同的动画
            execDataNowAnimation(isNewParagraph, holder, data)
            speakerStyle?.let { holder.setSpeakerStyle(it) }
        } else if (holder is ViewHolderError) {
            holder.updateData(context, data)
        }
    }


    private fun execDataNowAnimation(
        isNewParagraph: Boolean,
        holder: ViewHolderDataNow,
        data: DisplaySubtitleEntry?
    ) {
        if (data?.originContent?.roleId == null || data.originContent.roleId < 0) return
        //执行json动画
        if (data.originContent.vadType == TYPE_INTERMEDIATE) {
            holder.startSpeakingAnimation(isNewParagraph)
        } else {
            holder.speakerRecognitionFinish(data.originContent.roleId != oldDataRoleId)
        }
        oldDataRoleId = data.originContent.roleId
    }

    private fun calculateSpeakerStyle(
        holder: RecyclerView.ViewHolder,
        data: DisplaySubtitleEntry
    ): Int {
        val speakerStyle = if (holder !is ViewHolderError) {
            speakerStyleHashMap.getOrPut(data.originContent.roleId) {
                if (speakerStyleHashMap.size < COLOR_MAX_COUNT) {
                    speakerColors[speakerStyleHashMap.size % speakerColors.size]
                } else {
                    speakerColors.random()
                }
            }
        } else {
            -1
        }
        return speakerStyle
    }

    /**
     * 获取item总数
     */
    override fun getItemCount(): Int {
        return dataList.size + 1
    }

    /**
     * 刷新数据
     */
    fun setData(data: MutableList<DisplaySubtitleEntry>) {
        if (data.isEmpty()) return
        DebugUtil.i(TAG, "setData =: " + data.size)
        dataList = data.toMutableList()
        subtitleScrollHelper?.handleDataUpdate()
    }

    fun setNoNetWork(isNONetWork: Boolean) {
        val item = ConvertContentItem()
        val emptyMarkList = mutableListOf<DisplayMark>()
        item.vadType = if (isNONetWork) TYPE_NOT_NET else TYPE_NOT_SERVICE
        dataList.add(DisplaySubtitleEntry(item, "", emptyMarkList))
        notifyItemChanged(dataList.size - 1)
    }

    fun removeNoNetWork() {
        val size = dataList.size
        DebugUtil.d(TAG, "removeNoNetWork dataList.size = $size")
        if (size > 0) {
            val item = dataList[size - 1]
            if (item.originContent.vadType == TYPE_NOT_NET || item.originContent.vadType == TYPE_NOT_SERVICE) {
                val removeResult = dataList.remove(item)
                if (removeResult) {
                    notifyItemRemoved(size - 1)
                }
                DebugUtil.i(TAG, "removeNoNetWork item type = ${item.originContent.vadType}, remove item result: $removeResult")
            }
        }
    }

    fun setListener(listener: OnItemClickListener) {
        this.listener = listener
    }

    interface OnItemClickListener {
        fun onItemClickSpeaker(position: Int)
    }

    class SpaceViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView)

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        if (recyclerView is COUIRecyclerView) {
            subtitleScrollHelper = SubtitleScrollHelper()
            subtitleScrollHelper?.attached(recyclerView, this)
        }
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        super.onDetachedFromRecyclerView(recyclerView)
        subtitleScrollHelper?.detached()
        subtitleScrollHelper = null
    }
}