package io.noties.markwon.ext.tasklist;

import android.graphics.drawable.Drawable;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import io.noties.markwon.MarkwonConfiguration;
import io.noties.markwon.RenderProps;
import io.noties.markwon.SpanFactory;
import io.noties.markwon.prop.SpaceProps;

public class TaskListSpanFactory implements SpanFactory {

    private final Drawable drawable;
    private final Drawable checkDrawable;

    public TaskListSpanFactory(@NonNull Drawable drawable) {
        this.drawable = drawable;
        checkDrawable = drawable;
    }

    public TaskListSpanFactory(@NonNull Drawable drawable, @NonNull Drawable checkdrawable) {
        this.drawable = drawable;
        this.checkDrawable = checkdrawable;
    }

    @Nullable
    @Override
    public Object getSpans(@NonNull MarkwonConfiguration configuration, @NonNull RenderProps props) {
        return new TaskListSpan(
                configuration.theme(),
                drawable,
                checkDrawable,
                TaskListProps.DONE.get(props, false),
                TaskListProps.CONTENT.get(props, ""),
                SpaceProps.Companion.getLIST_ITEM_TOP_SPACE().get(props, SpaceProps.LEVEL_NO_SPACE)
        );
    }
}
